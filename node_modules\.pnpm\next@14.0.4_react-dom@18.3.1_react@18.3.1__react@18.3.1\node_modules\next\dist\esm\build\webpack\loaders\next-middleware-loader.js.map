{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-middleware-loader.ts"], "names": ["getModuleBuildInfo", "MIDDLEWARE_LOCATION_REGEXP", "loadEntrypoint", "encodeMatchers", "matchers", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify", "toString", "decodeMatchers", "encodedMatchers", "parse", "middlewareLoader", "absolutePagePath", "page", "rootDir", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "getOptions", "undefined", "pagePath", "utils", "contextify", "context", "rootContext", "buildInfo", "_module", "nextEdgeMiddleware", "replace", "RegExp", "route", "VAR_USERLAND", "VAR_DEFINITION_PAGE"], "mappings": "AAIA,SAASA,kBAAkB,QAAQ,0BAAyB;AAC5D,SAASC,0BAA0B,QAAQ,yBAAwB;AACnE,SAASC,cAAc,QAAQ,wBAAuB;AAWtD,oEAAoE;AACpE,gDAAgD;AAChD,OAAO,SAASC,eAAeC,QAA6B;IAC1D,OAAOC,OAAOC,IAAI,CAACC,KAAKC,SAAS,CAACJ,WAAWK,QAAQ,CAAC;AACxD;AAEA,OAAO,SAASC,eAAeC,eAAuB;IACpD,OAAOJ,KAAKK,KAAK,CACfP,OAAOC,IAAI,CAACK,iBAAiB,UAAUF,QAAQ;AAEnD;AAEA,eAAe,eAAeI;IAC5B,MAAM,EACJC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPZ,UAAUO,eAAe,EACzBM,eAAe,EACfC,kBAAkBC,sBAAsB,EACzC,GAA4B,IAAI,CAACC,UAAU;IAC5C,MAAMhB,WAAWO,kBAAkBD,eAAeC,mBAAmBU;IACrE,MAAMC,WAAW,IAAI,CAACC,KAAK,CAACC,UAAU,CACpC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChCZ;IAGF,MAAMI,mBAAqCX,KAAKK,KAAK,CACnDP,OAAOC,IAAI,CAACa,wBAAwB,UAAUV,QAAQ;IAExD,MAAMkB,YAAY3B,mBAAmB,IAAI,CAAC4B,OAAO;IACjDD,UAAUE,kBAAkB,GAAG;QAC7BzB;QACAW,MACEA,KAAKe,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAE9B,2BAA2B,CAAC,CAAC,GAAG,OAAO;IACvE;IACA0B,UAAUX,OAAO,GAAGA;IACpBW,UAAUK,KAAK,GAAG;QAChBjB;QACAD;QACAG;QACAC;IACF;IAEA,OAAO,MAAMhB,eAAe,cAAc;QACxC+B,cAAcX;QACdY,qBAAqBnB;IACvB;AACF"}