{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/postcss-loader/src/utils.ts"], "names": ["path", "IS_NATIVE_WIN32_PATH", "ABSOLUTE_SCHEME", "getURLType", "source", "test", "normalizeSourceMap", "map", "resourceContext", "newMap", "JSON", "parse", "file", "sourceRoot", "sources", "sourceType", "absoluteSource", "resolve", "normalize", "relative", "normalizeSourceMapAfterPostcss", "indexOf"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AAEvB,MAAMC,uBAAuB;AAC7B,MAAMC,kBAAkB;AAExB,SAASC,WAAWC,MAAc;IAChC,IAAIA,MAAM,CAAC,EAAE,KAAK,KAAK;QACrB,IAAIA,MAAM,CAAC,EAAE,KAAK,KAAK;YACrB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,IAAIH,qBAAqBI,IAAI,CAACD,SAAS;QACrC,OAAO;IACT;IAEA,OAAOF,gBAAgBG,IAAI,CAACD,UAAU,aAAa;AACrD;AAEA,SAASE,mBAAmBC,GAAQ,EAAEC,eAAuB;IAC3D,IAAIC,SAASF;IAEb,wCAAwC;IACxC,4IAA4I;IAC5I,IAAI,OAAOE,WAAW,UAAU;QAC9BA,SAASC,KAAKC,KAAK,CAACF;IACtB;IAEA,OAAOA,OAAOG,IAAI;IAElB,MAAM,EAAEC,UAAU,EAAE,GAAGJ;IAEvB,OAAOA,OAAOI,UAAU;IAExB,IAAIJ,OAAOK,OAAO,EAAE;QAClBL,OAAOK,OAAO,GAAGL,OAAOK,OAAO,CAACP,GAAG,CAAC,CAACH;YACnC,MAAMW,aAAaZ,WAAWC;YAE9B,oDAAoD;YACpD,IAAIW,eAAe,mBAAmBA,eAAe,iBAAiB;gBACpE,MAAMC,iBACJD,eAAe,mBAAmBF,aAC9Bb,KAAKiB,OAAO,CAACJ,YAAYb,KAAKkB,SAAS,CAACd,WACxCJ,KAAKkB,SAAS,CAACd;gBAErB,OAAOJ,KAAKmB,QAAQ,CAACX,iBAAiBQ;YACxC;YAEA,OAAOZ;QACT;IACF;IAEA,OAAOK;AACT;AAEA,SAASW,+BAA+Bb,GAAQ,EAAEC,eAAuB;IACvE,MAAMC,SAASF;IAEf,6EAA6E;IAC7E,uGAAuG;IACvG,6CAA6C;IAC7C,OAAOE,OAAOG,IAAI;IAElB,6CAA6C;IAC7CH,OAAOI,UAAU,GAAG;IAEpB,6CAA6C;IAC7CJ,OAAOK,OAAO,GAAGL,OAAOK,OAAO,CAACP,GAAG,CAAC,CAACH;QACnC,IAAIA,OAAOiB,OAAO,CAAC,SAAS,GAAG;YAC7B,OAAOjB;QACT;QAEA,MAAMW,aAAaZ,WAAWC;QAE9B,sEAAsE;QACtE,IAAIW,eAAe,iBAAiB;YAClC,OAAOf,KAAKiB,OAAO,CAACT,iBAAiBJ;QACvC;QAEA,OAAOA;IACT;IAEA,OAAOK;AACT;AAEA,SAASH,kBAAkB,EAAEc,8BAA8B,GAAE"}