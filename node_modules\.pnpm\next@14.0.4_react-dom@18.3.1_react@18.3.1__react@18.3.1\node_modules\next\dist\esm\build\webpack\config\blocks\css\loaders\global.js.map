{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/global.ts"], "names": ["getClientStyleLoader", "cssFileResolve", "getGlobalCssLoader", "ctx", "postcss", "preProcessors", "loaders", "isClient", "push", "hasAppDir", "isAppDir", "isDevelopment", "assetPrefix", "loader", "require", "resolve", "options", "importLoaders", "length", "modules", "url", "resourcePath", "experimental", "urlImports", "import", "_", "slice", "reverse"], "mappings": "AAGA,SAASA,oBAAoB,QAAQ,WAAU;AAC/C,SAASC,cAAc,QAAQ,iBAAgB;AAE/C,OAAO,SAASC,mBACdC,GAAyB,EACzBC,OAAY,EACZC,gBAAmD,EAAE;IAErD,MAAMC,UAAoC,EAAE;IAE5C,IAAIH,IAAII,QAAQ,EAAE;QAChB,4DAA4D;QAC5D,SAAS;QACTD,QAAQE,IAAI,CACVR,qBAAqB;YACnBS,WAAWN,IAAIM,SAAS;YACxBC,UAAUP,IAAIO,QAAQ;YACtBC,eAAeR,IAAIQ,aAAa;YAChCC,aAAaT,IAAIS,WAAW;QAC9B;IAEJ;IAEA,sCAAsC;IACtCN,QAAQE,IAAI,CAAC;QACXK,QAAQC,QAAQC,OAAO,CAAC;QACxBC,SAAS;YACPZ;YACAa,eAAe,IAAIZ,cAAca,MAAM;YACvC,4CAA4C;YAC5CC,SAAS;YACTC,KAAK,CAACA,KAAaC,eACjBpB,eAAemB,KAAKC,cAAclB,IAAImB,YAAY,CAACC,UAAU;YAC/DC,QAAQ,CAACJ,KAAaK,GAAQJ,eAC5BpB,eAAemB,KAAKC,cAAclB,IAAImB,YAAY,CAACC,UAAU;QACjE;IACF;IAEA,cAAc;IACdjB,QAAQE,IAAI,CAAC;QACXK,QAAQC,QAAQC,OAAO,CAAC;QACxBC,SAAS;YACPZ;QACF;IACF;IAEAE,QAAQE,IAAI,CACV,sEAAsE;IACtE,0BAA0B;OACvBH,cAAcqB,KAAK,GAAGC,OAAO;IAGlC,OAAOrB;AACT"}