{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/metadata/discover.ts"], "names": ["path", "stringify", "STATIC_METADATA_IMAGES", "WEBPACK_RESOURCE_QUERIES", "METADATA_TYPE", "enumMetadataFiles", "dir", "filename", "extensions", "metadataResolver", "numericSuffix", "collectedFiles", "possibleFileNames", "concat", "Array", "fill", "map", "_", "index", "name", "resolved", "push", "createStaticMetadataFromRoute", "resolvedDir", "segment", "isRootLayoutOrRootPage", "pageExtensions", "basePath", "hasStaticMetadataFiles", "staticImagesMetadata", "icon", "apple", "twitter", "openGraph", "manifest", "undefined", "collectIconModuleIfExists", "type", "staticManifestExtension", "manifestFile", "length", "ext", "parse", "extension", "includes", "slice", "JSON", "resolvedMetadataFiles", "sort", "a", "b", "localeCompare", "for<PERSON>ach", "filepath", "imageModuleImportSource", "metadata", "imageModule", "unshift", "createMetadataExportsCode", "join"], "mappings": "AAIA,OAAOA,UAAU,OAAM;AACvB,SAASC,SAAS,QAAQ,cAAa;AACvC,SAASC,sBAAsB,QAAQ,6CAA4C;AACnF,SAASC,wBAAwB,QAAQ,4BAA2B;AAGpE,MAAMC,gBAAgB;AAEtB,mGAAmG;AACnG,eAAeC,kBACbC,GAAW,EACXC,QAAgB,EAChBC,UAA6B,EAC7B,EACEC,gBAAgB,EAChB,uFAAuF;AACvFC,aAAa,EAId;IAED,MAAMC,iBAA2B,EAAE;IAEnC,MAAMC,oBAAoB;QAACL;KAAS,CAACM,MAAM,CACzCH,gBACII,MAAM,IACHC,IAAI,CAAC,GACLC,GAAG,CAAC,CAACC,GAAGC,QAAUX,WAAWW,SAChC,EAAE;IAER,KAAK,MAAMC,QAAQP,kBAAmB;QACpC,MAAMQ,WAAW,MAAMX,iBAAiBH,KAAKa,MAAMX;QACnD,IAAIY,UAAU;YACZT,eAAeU,IAAI,CAACD;QACtB;IACF;IAEA,OAAOT;AACT;AAEA,OAAO,eAAeW,8BACpBC,WAAmB,EACnB,EACEC,OAAO,EACPf,gBAAgB,EAChBgB,sBAAsB,EACtBC,cAAc,EACdC,QAAQ,EAOT;IAED,IAAIC,yBAAyB;IAC7B,MAAMC,uBAA2C;QAC/CC,MAAM,EAAE;QACRC,OAAO,EAAE;QACTC,SAAS,EAAE;QACXC,WAAW,EAAE;QACbC,UAAUC;IACZ;IAEA,eAAeC,0BACbC,IAA8C;QAE9C,IAAIA,SAAS,YAAY;YACvB,MAAMC,0BAA0B;gBAAC;gBAAe;aAAO;YACvD,MAAMC,eAAe,MAAMlC,kBACzBkB,aACA,YACAe,wBAAwBzB,MAAM,CAACa,iBAC/B;gBAAEjB;gBAAkBC,eAAe;YAAM;YAE3C,IAAI6B,aAAaC,MAAM,GAAG,GAAG;gBAC3BZ,yBAAyB;gBACzB,MAAM,EAAET,IAAI,EAAEsB,GAAG,EAAE,GAAGzC,KAAK0C,KAAK,CAACH,YAAY,CAAC,EAAE;gBAChD,MAAMI,YAAYL,wBAAwBM,QAAQ,CAACH,IAAII,KAAK,CAAC,MACzDJ,IAAII,KAAK,CAAC,KACV;gBACJhB,qBAAqBK,QAAQ,GAAGY,KAAK7C,SAAS,CAAC,CAAC,CAAC,EAAEkB,KAAK,CAAC,EAAEwB,UAAU,CAAC;YACxE;YACA;QACF;QAEA,MAAMI,wBAAwB,MAAM1C,kBAClCkB,aACArB,sBAAsB,CAACmC,KAAK,CAAC9B,QAAQ,EACrC;eACKL,sBAAsB,CAACmC,KAAK,CAAC7B,UAAU;eACtC6B,SAAS,YAAY,EAAE,GAAGX;SAC/B,EACD;YAAEjB;YAAkBC,eAAe;QAAK;QAE1CqC,sBACGC,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEE,aAAa,CAACD,IAC/BE,OAAO,CAAC,CAACC;YACR,MAAMC,0BAA0B,CAAC,2BAA2B,EAAErD,UAC5D;gBACEoC;gBACAb;gBACAG;gBACAD;YACF,GAEA,CAAC,EAAE2B,SAAS,CAAC,EAAElD,yBAAyBoD,QAAQ,CAAC,CAAC;YAEpD,MAAMC,cAAc,CAAC,2DAA2D,EAAEV,KAAK7C,SAAS,CAC9FqD,yBACA,kBAAkB,CAAC;YACrB1B,yBAAyB;YACzB,IAAIS,SAAS,WAAW;gBACtBR,qBAAqBC,IAAI,CAAC2B,OAAO,CAACD;YACpC,OAAO;gBACL3B,oBAAoB,CAACQ,KAAK,CAAChB,IAAI,CAACmC;YAClC;QACF;IACJ;IAEA,iEAAiE;IACjE,MAAMpB,0BAA0B;IAChC,MAAMA,0BAA0B;IAChC,MAAMA,0BAA0B;IAChC,MAAMA,0BAA0B;IAChC,IAAIX,wBAAwB;QAC1B,MAAMW,0BAA0B;QAChC,MAAMA,0BAA0B;IAClC;IAEA,OAAOR,yBAAyBC,uBAAuB;AACzD;AAEA,OAAO,SAAS6B,0BACdH,QAAmE;IAEnE,OAAOA,WACH,CAAC,EAAEnD,cAAc;WACZ,EAAEmD,SAASzB,IAAI,CAAC6B,IAAI,CAAC,KAAK;YACzB,EAAEJ,SAASxB,KAAK,CAAC4B,IAAI,CAAC,KAAK;gBACvB,EAAEJ,SAAStB,SAAS,CAAC0B,IAAI,CAAC,KAAK;cACjC,EAAEJ,SAASvB,OAAO,CAAC2B,IAAI,CAAC,KAAK;cAC7B,EAAEJ,SAASrB,QAAQ,GAAGqB,SAASrB,QAAQ,GAAG,YAAY;GACjE,CAAC,GACE;AACN"}