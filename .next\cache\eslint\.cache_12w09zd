[{"C:\\Projects\\augment_code_v0_compare\\app\\account\\page.tsx": "1", "C:\\Projects\\augment_code_v0_compare\\app\\dashboard\\page.tsx": "2", "C:\\Projects\\augment_code_v0_compare\\app\\error.tsx": "3", "C:\\Projects\\augment_code_v0_compare\\app\\layout.tsx": "4", "C:\\Projects\\augment_code_v0_compare\\app\\not-found.tsx": "5", "C:\\Projects\\augment_code_v0_compare\\app\\page.tsx": "6", "C:\\Projects\\augment_code_v0_compare\\app\\privacy\\page.tsx": "7", "C:\\Projects\\augment_code_v0_compare\\app\\signin\\page.tsx": "8", "C:\\Projects\\augment_code_v0_compare\\app\\terms\\page.tsx": "9", "C:\\Projects\\augment_code_v0_compare\\app\\unauthorized\\page.tsx": "10", "C:\\Projects\\augment_code_v0_compare\\components\\Footer.tsx": "11", "C:\\Projects\\augment_code_v0_compare\\components\\Layout.tsx": "12", "C:\\Projects\\augment_code_v0_compare\\components\\LoadingSpinner.tsx": "13", "C:\\Projects\\augment_code_v0_compare\\components\\Navigation.tsx": "14", "C:\\Projects\\augment_code_v0_compare\\components\\PropertyCard.tsx": "15", "C:\\Projects\\augment_code_v0_compare\\lib\\auth.ts": "16", "C:\\Projects\\augment_code_v0_compare\\lib\\config.ts": "17"}, {"size": 3166, "mtime": 1755136256618, "results": "18", "hashOfConfig": "19"}, {"size": 1828, "mtime": 1755136245386, "results": "20", "hashOfConfig": "19"}, {"size": 1959, "mtime": 1755136280942, "results": "21", "hashOfConfig": "19"}, {"size": 646, "mtime": 1755136921403, "results": "22", "hashOfConfig": "19"}, {"size": 1339, "mtime": 1755136264010, "results": "23", "hashOfConfig": "19"}, {"size": 1686, "mtime": 1755136223311, "results": "24", "hashOfConfig": "19"}, {"size": 3585, "mtime": 1755136719449, "results": "25", "hashOfConfig": "19"}, {"size": 3106, "mtime": 1755136236610, "results": "26", "hashOfConfig": "19"}, {"size": 4288, "mtime": 1755136728099, "results": "27", "hashOfConfig": "19"}, {"size": 1449, "mtime": 1755136271982, "results": "28", "hashOfConfig": "19"}, {"size": 1650, "mtime": 1755136201091, "results": "29", "hashOfConfig": "19"}, {"size": 574, "mtime": 1755136205671, "results": "30", "hashOfConfig": "19"}, {"size": 1444, "mtime": 1755136178139, "results": "31", "hashOfConfig": "19"}, {"size": 3689, "mtime": 1755136193050, "results": "32", "hashOfConfig": "19"}, {"size": 1958, "mtime": 1755136706418, "results": "33", "hashOfConfig": "19"}, {"size": 1424, "mtime": 1755136146769, "results": "34", "hashOfConfig": "19"}, {"size": 3362, "mtime": 1755136137800, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "o5bysg", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Projects\\augment_code_v0_compare\\app\\account\\page.tsx", [], [], "C:\\Projects\\augment_code_v0_compare\\app\\dashboard\\page.tsx", [], [], "C:\\Projects\\augment_code_v0_compare\\app\\error.tsx", [], [], "C:\\Projects\\augment_code_v0_compare\\app\\layout.tsx", [], [], "C:\\Projects\\augment_code_v0_compare\\app\\not-found.tsx", ["87", "88"], [], "C:\\Projects\\augment_code_v0_compare\\app\\page.tsx", [], [], "C:\\Projects\\augment_code_v0_compare\\app\\privacy\\page.tsx", ["89", "90", "91", "92", "93", "94"], [], "C:\\Projects\\augment_code_v0_compare\\app\\signin\\page.tsx", [], [], "C:\\Projects\\augment_code_v0_compare\\app\\terms\\page.tsx", ["95", "96"], [], "C:\\Projects\\augment_code_v0_compare\\app\\unauthorized\\page.tsx", ["97"], [], "C:\\Projects\\augment_code_v0_compare\\components\\Footer.tsx", [], [], "C:\\Projects\\augment_code_v0_compare\\components\\Layout.tsx", [], [], "C:\\Projects\\augment_code_v0_compare\\components\\LoadingSpinner.tsx", [], [], "C:\\Projects\\augment_code_v0_compare\\components\\Navigation.tsx", [], [], "C:\\Projects\\augment_code_v0_compare\\components\\PropertyCard.tsx", [], [], "C:\\Projects\\augment_code_v0_compare\\lib\\auth.ts", [], [], "C:\\Projects\\augment_code_v0_compare\\lib\\config.ts", [], [], {"ruleId": "98", "severity": 2, "message": "99", "line": 16, "column": 27, "nodeType": "100", "messageId": "101", "suggestions": "102"}, {"ruleId": "98", "severity": 2, "message": "99", "line": 16, "column": 48, "nodeType": "100", "messageId": "101", "suggestions": "103"}, {"ruleId": "98", "severity": 2, "message": "104", "line": 22, "column": 34, "nodeType": "100", "messageId": "101", "suggestions": "105"}, {"ruleId": "98", "severity": 2, "message": "104", "line": 22, "column": 38, "nodeType": "100", "messageId": "101", "suggestions": "106"}, {"ruleId": "98", "severity": 2, "message": "104", "line": 22, "column": 40, "nodeType": "100", "messageId": "101", "suggestions": "107"}, {"ruleId": "98", "severity": 2, "message": "104", "line": 22, "column": 45, "nodeType": "100", "messageId": "101", "suggestions": "108"}, {"ruleId": "98", "severity": 2, "message": "104", "line": 22, "column": 50, "nodeType": "100", "messageId": "101", "suggestions": "109"}, {"ruleId": "98", "severity": 2, "message": "104", "line": 22, "column": 53, "nodeType": "100", "messageId": "101", "suggestions": "110"}, {"ruleId": "98", "severity": 2, "message": "99", "line": 67, "column": 69, "nodeType": "100", "messageId": "101", "suggestions": "111"}, {"ruleId": "98", "severity": 2, "message": "99", "line": 67, "column": 75, "nodeType": "100", "messageId": "101", "suggestions": "112"}, {"ruleId": "98", "severity": 2, "message": "99", "line": 18, "column": 22, "nodeType": "100", "messageId": "101", "suggestions": "113"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["114", "115", "116", "117"], ["118", "119", "120", "121"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["122", "123", "124", "125"], ["126", "127", "128", "129"], ["130", "131", "132", "133"], ["134", "135", "136", "137"], ["138", "139", "140", "141"], ["142", "143", "144", "145"], ["146", "147", "148", "149"], ["150", "151", "152", "153"], ["154", "155", "156", "157"], {"messageId": "158", "data": "159", "fix": "160", "desc": "161"}, {"messageId": "158", "data": "162", "fix": "163", "desc": "164"}, {"messageId": "158", "data": "165", "fix": "166", "desc": "167"}, {"messageId": "158", "data": "168", "fix": "169", "desc": "170"}, {"messageId": "158", "data": "171", "fix": "172", "desc": "161"}, {"messageId": "158", "data": "173", "fix": "174", "desc": "164"}, {"messageId": "158", "data": "175", "fix": "176", "desc": "167"}, {"messageId": "158", "data": "177", "fix": "178", "desc": "170"}, {"messageId": "158", "data": "179", "fix": "180", "desc": "181"}, {"messageId": "158", "data": "182", "fix": "183", "desc": "184"}, {"messageId": "158", "data": "185", "fix": "186", "desc": "187"}, {"messageId": "158", "data": "188", "fix": "189", "desc": "190"}, {"messageId": "158", "data": "191", "fix": "192", "desc": "181"}, {"messageId": "158", "data": "193", "fix": "194", "desc": "184"}, {"messageId": "158", "data": "195", "fix": "196", "desc": "187"}, {"messageId": "158", "data": "197", "fix": "198", "desc": "190"}, {"messageId": "158", "data": "199", "fix": "200", "desc": "181"}, {"messageId": "158", "data": "201", "fix": "202", "desc": "184"}, {"messageId": "158", "data": "203", "fix": "204", "desc": "187"}, {"messageId": "158", "data": "205", "fix": "206", "desc": "190"}, {"messageId": "158", "data": "207", "fix": "208", "desc": "181"}, {"messageId": "158", "data": "209", "fix": "210", "desc": "184"}, {"messageId": "158", "data": "211", "fix": "212", "desc": "187"}, {"messageId": "158", "data": "213", "fix": "214", "desc": "190"}, {"messageId": "158", "data": "215", "fix": "216", "desc": "181"}, {"messageId": "158", "data": "217", "fix": "218", "desc": "184"}, {"messageId": "158", "data": "219", "fix": "220", "desc": "187"}, {"messageId": "158", "data": "221", "fix": "222", "desc": "190"}, {"messageId": "158", "data": "223", "fix": "224", "desc": "181"}, {"messageId": "158", "data": "225", "fix": "226", "desc": "184"}, {"messageId": "158", "data": "227", "fix": "228", "desc": "187"}, {"messageId": "158", "data": "229", "fix": "230", "desc": "190"}, {"messageId": "158", "data": "231", "fix": "232", "desc": "161"}, {"messageId": "158", "data": "233", "fix": "234", "desc": "164"}, {"messageId": "158", "data": "235", "fix": "236", "desc": "167"}, {"messageId": "158", "data": "237", "fix": "238", "desc": "170"}, {"messageId": "158", "data": "239", "fix": "240", "desc": "161"}, {"messageId": "158", "data": "241", "fix": "242", "desc": "164"}, {"messageId": "158", "data": "243", "fix": "244", "desc": "167"}, {"messageId": "158", "data": "245", "fix": "246", "desc": "170"}, {"messageId": "158", "data": "247", "fix": "248", "desc": "161"}, {"messageId": "158", "data": "249", "fix": "250", "desc": "164"}, {"messageId": "158", "data": "251", "fix": "252", "desc": "167"}, {"messageId": "158", "data": "253", "fix": "254", "desc": "170"}, "replaceWithAlt", {"alt": "255"}, {"range": "256", "text": "257"}, "Replace with `&apos;`.", {"alt": "258"}, {"range": "259", "text": "260"}, "Replace with `&lsquo;`.", {"alt": "261"}, {"range": "262", "text": "263"}, "Replace with `&#39;`.", {"alt": "264"}, {"range": "265", "text": "266"}, "Replace with `&rsquo;`.", {"alt": "255"}, {"range": "267", "text": "268"}, {"alt": "258"}, {"range": "269", "text": "270"}, {"alt": "261"}, {"range": "271", "text": "272"}, {"alt": "264"}, {"range": "273", "text": "274"}, {"alt": "275"}, {"range": "276", "text": "277"}, "Replace with `&quot;`.", {"alt": "278"}, {"range": "279", "text": "280"}, "Replace with `&ldquo;`.", {"alt": "281"}, {"range": "282", "text": "283"}, "Replace with `&#34;`.", {"alt": "284"}, {"range": "285", "text": "286"}, "Replace with `&rdquo;`.", {"alt": "275"}, {"range": "287", "text": "288"}, {"alt": "278"}, {"range": "289", "text": "290"}, {"alt": "281"}, {"range": "291", "text": "292"}, {"alt": "284"}, {"range": "293", "text": "294"}, {"alt": "275"}, {"range": "295", "text": "296"}, {"alt": "278"}, {"range": "297", "text": "298"}, {"alt": "281"}, {"range": "299", "text": "300"}, {"alt": "284"}, {"range": "301", "text": "302"}, {"alt": "275"}, {"range": "303", "text": "304"}, {"alt": "278"}, {"range": "305", "text": "306"}, {"alt": "281"}, {"range": "307", "text": "308"}, {"alt": "284"}, {"range": "309", "text": "310"}, {"alt": "275"}, {"range": "311", "text": "312"}, {"alt": "278"}, {"range": "313", "text": "314"}, {"alt": "281"}, {"range": "315", "text": "316"}, {"alt": "284"}, {"range": "317", "text": "318"}, {"alt": "275"}, {"range": "319", "text": "320"}, {"alt": "278"}, {"range": "321", "text": "322"}, {"alt": "281"}, {"range": "323", "text": "324"}, {"alt": "284"}, {"range": "325", "text": "326"}, {"alt": "255"}, {"range": "327", "text": "328"}, {"alt": "258"}, {"range": "329", "text": "330"}, {"alt": "261"}, {"range": "331", "text": "332"}, {"alt": "264"}, {"range": "333", "text": "334"}, {"alt": "255"}, {"range": "335", "text": "336"}, {"alt": "258"}, {"range": "337", "text": "338"}, {"alt": "261"}, {"range": "339", "text": "340"}, {"alt": "264"}, {"range": "341", "text": "342"}, {"alt": "255"}, {"range": "343", "text": "344"}, {"alt": "258"}, {"range": "345", "text": "346"}, {"alt": "261"}, {"range": "347", "text": "348"}, {"alt": "264"}, {"range": "349", "text": "350"}, "&apos;", [608, 720], "\n              The page you&apos;re looking for doesn't exist or has been moved to a different location.\n            ", "&lsquo;", [608, 720], "\n              The page you&lsquo;re looking for doesn't exist or has been moved to a different location.\n            ", "&#39;", [608, 720], "\n              The page you&#39;re looking for doesn't exist or has been moved to a different location.\n            ", "&rsquo;", [608, 720], "\n              The page you&rsquo;re looking for doesn't exist or has been moved to a different location.\n            ", [608, 720], "\n              The page you're looking for doesn&apos;t exist or has been moved to a different location.\n            ", [608, 720], "\n              The page you're looking for doesn&lsquo;t exist or has been moved to a different location.\n            ", [608, 720], "\n              The page you're looking for doesn&#39;t exist or has been moved to a different location.\n            ", [608, 720], "\n              The page you're looking for doesn&rsquo;t exist or has been moved to a different location.\n            ", "&quot;", [746, 993], " (&quot;we,\" \"our,\" or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", "&ldquo;", [746, 993], " (&ldquo;we,\" \"our,\" or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", "&#34;", [746, 993], " (&#34;we,\" \"our,\" or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", "&rdquo;", [746, 993], " (&rdquo;we,\" \"our,\" or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,&quot; \"our,\" or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,&ldquo; \"our,\" or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,&#34; \"our,\" or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,&rdquo; \"our,\" or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" &quot;our,\" or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" &ldquo;our,\" or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" &#34;our,\" or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" &rdquo;our,\" or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" \"our,&quot; or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" \"our,&ldquo; or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" \"our,&#34; or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" \"our,&rdquo; or \"us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" \"our,\" or &quot;us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" \"our,\" or &ldquo;us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" \"our,\" or &#34;us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" \"our,\" or &rdquo;us\") is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" \"our,\" or \"us&quot;) is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" \"our,\" or \"us&ldquo;) is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" \"our,\" or \"us&#34;) is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [746, 993], " (\"we,\" \"our,\" or \"us&rdquo;) is committed to protecting your privacy. \n              This Privacy Policy explains how we collect, use, disclose, and safeguard your \n              information when you visit our website and use our services.\n            ", [3228, 3263], " are provided on an &apos;as is' basis. ", [3228, 3263], " are provided on an &lsquo;as is' basis. ", [3228, 3263], " are provided on an &#39;as is' basis. ", [3228, 3263], " are provided on an &rsquo;as is' basis. ", [3228, 3263], " are provided on an 'as is&apos; basis. ", [3228, 3263], " are provided on an 'as is&lsquo; basis. ", [3228, 3263], " are provided on an 'as is&#39; basis. ", [3228, 3263], " are provided on an 'as is&rsquo; basis. ", [732, 861], "\n              You don&apos;t have permission to access this page. Please sign in with an authorized account to continue.\n            ", [732, 861], "\n              You don&lsquo;t have permission to access this page. Please sign in with an authorized account to continue.\n            ", [732, 861], "\n              You don&#39;t have permission to access this page. Please sign in with an authorized account to continue.\n            ", [732, 861], "\n              You don&rsquo;t have permission to access this page. Please sign in with an authorized account to continue.\n            "]