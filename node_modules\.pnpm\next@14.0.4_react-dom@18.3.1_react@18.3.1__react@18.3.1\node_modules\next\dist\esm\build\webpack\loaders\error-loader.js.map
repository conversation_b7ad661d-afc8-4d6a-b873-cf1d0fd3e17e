{"version": 3, "sources": ["../../../../src/build/webpack/loaders/error-loader.ts"], "names": ["cyan", "path", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options", "getOptions", "reason", "resource", "_module", "issuer", "context", "rootContext", "_compiler", "relative", "err", "Error", "emitError"], "mappings": "AAAA,SAASA,IAAI,QAAQ,0BAAyB;AAC9C,OAAOC,UAAU,OAAM;AAGvB,MAAMC,cAAgD;QAOnC,sBAAA,eACmB;IAPpC,oBAAoB;IACpB,MAAMC,UAAU,IAAI,CAACC,UAAU,MAAO,CAAC;IAEvC,MAAM,EAAEC,SAAS,+BAA+B,EAAE,GAAGF;IAErD,mBAAmB;IACnB,MAAMG,WAAW,EAAA,gBAAA,IAAI,CAACC,OAAO,sBAAZ,uBAAA,cAAcC,MAAM,qBAApB,qBAAsBF,QAAQ,KAAI;IACnD,MAAMG,UAAU,IAAI,CAACC,WAAW,MAAI,kBAAA,IAAI,CAACC,SAAS,qBAAd,gBAAgBF,OAAO;IAE3D,MAAMD,SAASF,WACXG,UACER,KAAKW,QAAQ,CAACH,SAASH,YACvBA,WACF;IAEJ,MAAMO,MAAM,IAAIC,MAAMT,SAAUG,CAAAA,SAAS,CAAC,YAAY,EAAER,KAAKQ,QAAQ,CAAC,GAAG,EAAC;IAC1E,IAAI,CAACO,SAAS,CAACF;AACjB;AAEA,eAAeX,YAAW"}