@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Zinc color palette - default theme */
  --color-primary-50: 250 250 250;
  --color-primary-100: 244 244 245;
  --color-primary-200: 228 228 231;
  --color-primary-300: 212 212 216;
  --color-primary-400: 161 161 170;
  --color-primary-500: 113 113 122;
  --color-primary-600: 82 82 91;
  --color-primary-700: 63 63 70;
  --color-primary-800: 39 39 42;
  --color-primary-900: 24 24 27;
  --color-primary-950: 9 9 11;
}

@layer base {
  html {
    font-family: var(--font-geist-sans), system-ui, sans-serif;
  }
  
  body {
    @apply bg-white text-primary-900 antialiased;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-900 text-white px-4 py-2 rounded-lg hover:bg-primary-800 transition-colors duration-200 font-medium;
  }
  
  .btn-secondary {
    @apply bg-primary-100 text-primary-900 px-4 py-2 rounded-lg hover:bg-primary-200 transition-colors duration-200 font-medium;
  }
  
  .card {
    @apply bg-white border border-primary-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-primary-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  .page-container {
    @apply min-h-screen flex flex-col;
  }
  
  .main-content {
    @apply flex-1 px-4 sm:px-6 lg:px-8 py-8;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-primary-200 border-t-primary-600;
  }
}
