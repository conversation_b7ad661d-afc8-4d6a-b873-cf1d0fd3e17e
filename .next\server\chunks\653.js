exports.id=653,exports.ids=[653],exports.modules={8721:(e,t,s)=>{Promise.resolve().then(s.bind(s,4788))},2807:(e,t,s)=>{Promise.resolve().then(s.bind(s,6520))},7377:(e,t,s)=>{Promise.resolve().then(s.bind(s,466))},4865:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6701,23)),Promise.resolve().then(s.t.bind(s,7656,23)),Promise.resolve().then(s.t.bind(s,9306,23)),Promise.resolve().then(s.t.bind(s,9810,23)),Promise.resolve().then(s.t.bind(s,5525,23)),Promise.resolve().then(s.t.bind(s,5990,23))},4788:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(4353),a=s(2566),i=s(948),n=s(9962),o=s(1158),c=s(208),l=s(3038),m=s.n(l);function d({error:e,reset:t}){return(0,a.useEffect)(()=>{console.error("Application error:",e)},[e]),r.jsx(i.Layout,{children:r.jsx("div",{className:"min-h-[60vh] flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("div",{className:"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:r.jsx(n.Z,{size:32,className:"text-red-600"})}),r.jsx("h1",{className:"text-3xl font-bold text-primary-900 mb-4",children:"Something went wrong"}),r.jsx("p",{className:"text-primary-600 leading-relaxed mb-4",children:"An unexpected error occurred while processing your request. Please try again or contact support if the problem persists."}),e.digest&&(0,r.jsxs)("p",{className:"text-xs text-primary-400 font-mono bg-primary-50 p-2 rounded",children:["Error ID: ",e.digest]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("button",{onClick:t,className:"btn-primary inline-flex items-center space-x-2",children:[r.jsx(o.Z,{size:16}),r.jsx("span",{children:"Try Again"})]}),(0,r.jsxs)(m(),{href:"/",className:"btn-secondary inline-flex items-center space-x-2 ml-4",children:[r.jsx(c.Z,{size:16}),r.jsx("span",{children:"Go Home"})]})]})]})})})}},466:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(4353),a=s(3038),i=s.n(a),n=s(948),o=s(208),c=s(7127);function l(){return r.jsx(n.Layout,{children:r.jsx("div",{className:"min-h-[60vh] flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-6xl font-bold text-primary-300 mb-4",children:"404"}),r.jsx("h2",{className:"text-2xl font-semibold text-primary-900 mb-4",children:"Page Not Found"}),r.jsx("p",{className:"text-primary-600 leading-relaxed",children:"The page you're looking for doesn't exist or has been moved to a different location."})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)(i(),{href:"/",className:"btn-primary inline-flex items-center space-x-2",children:[r.jsx(o.Z,{size:16}),r.jsx("span",{children:"Go Home"})]}),(0,r.jsxs)("button",{onClick:()=>window.history.back(),className:"btn-secondary inline-flex items-center space-x-2 ml-4",children:[r.jsx(c.Z,{size:16}),r.jsx("span",{children:"Go Back"})]})]})]})})})}},948:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Layout:()=>y});var r=s(4353),a=s(3038),i=s.n(a),n=s(8596),o=s(6520),c=s(6220),l=s(208),m=s(3632),d=s(7774),x=s(9838),h=s(8735),p=s(2566);function u(){let{user:e,signOut:t}=(0,o.useAuth)(),s=(0,n.usePathname)(),a=(0,n.useRouter)(),[u,f]=(0,p.useState)(!1),y=async()=>{f(!0);try{await t(),a.push("/")}finally{f(!1)}};return r.jsx("nav",{className:"bg-white border-b border-primary-200 sticky top-0 z-40",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[r.jsx(i(),{href:"/",className:"text-xl font-bold text-primary-900 hover:text-primary-700 transition-colors",children:c.JA.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(i(),{href:"/",className:`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${"/"===s?"bg-primary-100 text-primary-900":"text-primary-600 hover:text-primary-900 hover:bg-primary-50"}`,children:[r.jsx(l.Z,{size:16}),r.jsx("span",{children:"Home"})]}),e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(i(),{href:"/dashboard",className:`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${"/dashboard"===s?"bg-primary-100 text-primary-900":"text-primary-600 hover:text-primary-900 hover:bg-primary-50"}`,children:[r.jsx(m.Z,{size:16}),r.jsx("span",{children:"Dashboard"})]}),(0,r.jsxs)(i(),{href:"/account",className:`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${"/account"===s?"bg-primary-100 text-primary-900":"text-primary-600 hover:text-primary-900 hover:bg-primary-50"}`,children:[r.jsx(d.Z,{size:16}),r.jsx("span",{children:"Account"})]}),r.jsx(h.D1,{loading:u,children:(0,r.jsxs)("button",{onClick:y,className:"flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium text-primary-600 hover:text-primary-900 hover:bg-primary-50 transition-colors",disabled:u,children:[r.jsx(x.Z,{size:16}),r.jsx("span",{children:"Sign Out"})]})})]}):r.jsx(i(),{href:"/signin",className:"btn-primary",children:"Sign In"})]})]})})})}function f(){return r.jsx("footer",{className:"bg-primary-50 border-t border-primary-200 mt-auto",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-primary-900 mb-2",children:c.JA.name}),r.jsx("p",{className:"text-primary-600 text-sm leading-relaxed",children:c.JA.missionStatement})]}),(0,r.jsxs)("div",{className:"md:text-right",children:[r.jsx("h4",{className:"text-sm font-semibold text-primary-900 mb-3",children:"Legal"}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(i(),{href:"/privacy",className:"block text-sm text-primary-600 hover:text-primary-900 transition-colors",children:"Privacy Policy"}),r.jsx(i(),{href:"/terms",className:"block text-sm text-primary-600 hover:text-primary-900 transition-colors",children:"Terms of Service"})]})]})]}),r.jsx("div",{className:"mt-8 pt-8 border-t border-primary-200",children:(0,r.jsxs)("p",{className:"text-center text-sm text-primary-500",children:["\xa9 ",new Date().getFullYear()," ",c.JA.name,". All rights reserved."]})})]})})}function y({children:e,title:t}){return(0,r.jsxs)("div",{className:"page-container",children:[r.jsx(u,{}),(0,r.jsxs)("main",{className:"main-content",children:[t&&r.jsx("div",{className:"mb-8",children:r.jsx("h1",{className:"text-3xl font-bold text-primary-900",children:t})}),e]}),r.jsx(f,{})]})}},8735:(e,t,s)=>{"use strict";s.d(t,{D1:()=>n});var r=s(4353),a=s(2566);function i({size:e="md",delay:t=0,show:s=!0}){let[i,n]=(0,a.useState)(0===t);return((0,a.useEffect)(()=>{if(t>0&&s){let e=setTimeout(()=>n(!0),t);return()=>clearTimeout(e)}},[t,s]),i&&s)?r.jsx("div",{className:`loading-spinner ${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]}`}):null}function n({children:e,loading:t}){return(0,r.jsxs)("div",{className:"relative",children:[e,t&&r.jsx("div",{className:"absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center rounded-lg",children:r.jsx(i,{size:"sm",delay:500})})]})}},6520:(e,t,s)=>{"use strict";s.r(t),s.d(t,{AuthProvider:()=>x,useAuth:()=>h});var r=s(4353),a=s(2566);let i={id:"1",email:"<EMAIL>",name:"Admin User"},n=(e=1e3)=>new Promise(t=>setTimeout(t,e)),o=async(e,t)=>{if(await n(800),"<EMAIL>"===e&&"password"===t)return i;throw Error("Invalid credentials")},c=async()=>{await n(500)},l=()=>null,m=e=>{},d=(0,a.createContext)(void 0);function x({children:e}){let[t,s]=(0,a.useState)(null),[i,n]=(0,a.useState)(!0);(0,a.useEffect)(()=>{s(l()),n(!1)},[]);let x=async(e,t)=>{n(!0);try{let r=await o(e,t);s(r),m(r)}catch(e){throw e}finally{n(!1)}},h=async()=>{n(!0);try{await c(),s(null),m(null)}finally{n(!1)}};return r.jsx(d.Provider,{value:{user:t,loading:i,signIn:x,signOut:h},children:e})}function h(){let e=(0,a.useContext)(d);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},6220:(e,t,s)=>{"use strict";s.d(t,{$o:()=>a,JA:()=>r});let r={name:"St Cloud Enterprises Portal",description:"Managing access to website properties with excellence and innovation",missionStatement:"Empowering businesses through innovative web solutions and comprehensive property management services."},a=[{id:1,name:"E-Commerce Solutions Hub",description:"Comprehensive online retail platform with advanced inventory management and customer analytics.",image:"https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=400&h=200&fit=crop",establishedDate:"2020-03-15",url:"https://ecommerce.stcloud.com"},{id:2,name:"Corporate Services Portal",description:"Professional services platform offering consulting, project management, and business solutions.",image:"https://images.unsplash.com/photo-1497366216548-37526070297c?w=400&h=200&fit=crop",establishedDate:"2019-08-22",url:"https://corporate.stcloud.com"},{id:3,name:"Digital Marketing Suite",description:"Complete digital marketing platform with SEO tools, analytics, and campaign management.",image:"https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=200&fit=crop",establishedDate:"2021-01-10",url:"https://marketing.stcloud.com"},{id:4,name:"Real Estate Network",description:"Property management and real estate services with virtual tours and market analysis.",image:"https://images.unsplash.com/photo-**********-ce09059eeffa?w=400&h=200&fit=crop",establishedDate:"2018-11-05",url:"https://realestate.stcloud.com"},{id:5,name:"Healthcare Connect",description:"Telemedicine platform connecting patients with healthcare providers and medical resources.",image:"https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=200&fit=crop",establishedDate:"2022-06-18",url:"https://healthcare.stcloud.com"},{id:6,name:"Educational Resources",description:"Online learning platform with courses, certifications, and educational content management.",image:"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=200&fit=crop",establishedDate:"2020-09-12",url:"https://education.stcloud.com"}]},8363:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,s(8164).createProxy)(String.raw`C:\Projects\augment_code_v0_compare\app\error.tsx`),{__esModule:a,$$typeof:i}=r,n=r.default},6013:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,metadata:()=>p});var r=s(4699),a=s(1187),i=s.n(a),n=s(1245),o=s.n(n);s(1417);var c=s(8164);let l=(0,c.createProxy)(String.raw`C:\Projects\augment_code_v0_compare\contexts\AuthContext.tsx`),{__esModule:m,$$typeof:d}=l;l.default;let x=(0,c.createProxy)(String.raw`C:\Projects\augment_code_v0_compare\contexts\AuthContext.tsx#AuthProvider`);(0,c.createProxy)(String.raw`C:\Projects\augment_code_v0_compare\contexts\AuthContext.tsx#useAuth`);var h=s(5778);let p={title:h.JA.name,description:h.JA.description};function u({children:e}){return r.jsx("html",{lang:"en",className:`${i().variable} ${o().variable}`,children:r.jsx("body",{children:r.jsx(x,{children:e})})})}},1737:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,s(8164).createProxy)(String.raw`C:\Projects\augment_code_v0_compare\app\not-found.tsx`),{__esModule:a,$$typeof:i}=r,n=r.default},5778:(e,t,s)=>{"use strict";s.d(t,{JA:()=>r});let r={name:"St Cloud Enterprises Portal",description:"Managing access to website properties with excellence and innovation",missionStatement:"Empowering businesses through innovative web solutions and comprehensive property management services."}},1417:()=>{}};