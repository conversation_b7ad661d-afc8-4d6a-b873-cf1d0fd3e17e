{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-metadata-route-loader.ts"], "names": ["fs", "path", "imageExtMimeTypeMap", "errorOnBadHandler", "resourcePath", "JSON", "stringify", "cacheHeader", "none", "longCache", "revalidate", "getFilenameAndExtension", "filename", "basename", "name", "ext", "split", "getContentType", "getStaticAssetRouteCode", "fileBaseName", "cache", "process", "env", "NODE_ENV", "code", "promises", "readFile", "toString", "getDynamicTextRouteCode", "getDynamicImageRouteCode", "getDynamicSiteMapRouteCode", "page", "staticGenerationCode", "includes", "nextMetadataRouterLoader", "isDynamic", "getOptions"], "mappings": "AACA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,SAASC,mBAAmB,QAAQ,yBAAwB;AAE5D,SAASC,kBAAkBC,YAAoB;IAC7C,OAAO,CAAC;;kDAEwC,EAAEC,KAAKC,SAAS,CAC5DF,cACA;;EAEJ,CAAC;AACH;AAEA,MAAMG,cAAc;IAClBC,MAAM;IACNC,WAAW;IACXC,YAAY;AACd;AAOA,OAAO,SAASC,wBAAwBP,YAAoB;IAC1D,MAAMQ,WAAWX,KAAKY,QAAQ,CAACT;IAC/B,MAAM,CAACU,MAAMC,IAAI,GAAGH,SAASI,KAAK,CAAC,KAAK;IACxC,OAAO;QAAEF;QAAMC;IAAI;AACrB;AAEA,SAASE,eAAeb,YAAoB;IAC1C,IAAI,EAAEU,IAAI,EAAEC,GAAG,EAAE,GAAGJ,wBAAwBP;IAC5C,IAAIW,QAAQ,OAAOA,MAAM;IAEzB,IAAID,SAAS,aAAaC,QAAQ,OAAO,OAAO;IAChD,IAAID,SAAS,WAAW,OAAO;IAC/B,IAAIA,SAAS,UAAU,OAAO;IAC9B,IAAIA,SAAS,YAAY,OAAO;IAEhC,IAAIC,QAAQ,SAASA,QAAQ,UAAUA,QAAQ,SAASA,QAAQ,OAAO;QACrE,OAAOb,mBAAmB,CAACa,IAAI;IACjC;IACA,OAAO;AACT;AAEA,mHAAmH;AACnH,eAAeG,wBACbd,YAAoB,EACpBe,YAAoB;IAEpB,MAAMC,QACJD,iBAAiB,YACb,uCACAE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzBhB,YAAYC,IAAI,GAChBD,YAAYE,SAAS;IAC3B,MAAMe,OAAO,CAAC;;;oBAGI,EAAEnB,KAAKC,SAAS,CAACW,eAAeb,eAAe;2BACxC,EAAEC,KAAKC,SAAS,CACvC,AAAC,CAAA,MAAMN,GAAGyB,QAAQ,CAACC,QAAQ,CAACtB,aAAY,EAAGuB,QAAQ,CAAC,WACpD;;;;;;;uBAOmB,EAAEtB,KAAKC,SAAS,CAACc,OAAO;;;;;;AAM/C,CAAC;IACC,OAAOI;AACT;AAEA,SAASI,wBAAwBxB,YAAoB;IACnD,OAAO,CAAC;;oBAEU,EAAEC,KAAKC,SAAS,CAACF,cAAc;;;oBAG/B,EAAEC,KAAKC,SAAS,CAACW,eAAeb,eAAe;iBAClD,EAAEC,KAAKC,SAAS,CAACK,wBAAwBP,cAAcU,IAAI,EAAE;;AAE9E,EAAEX,kBAAkBC,cAAc;;;;;;;;;uBASX,EAAEC,KAAKC,SAAS,CAACC,YAAYG,UAAU,EAAE;;;;AAIhE,CAAC;AACD;AAEA,iCAAiC;AACjC,SAASmB,yBAAyBzB,YAAoB;IACpD,OAAO,CAAC;;0BAEgB,EAAEC,KAAKC,SAAS,CAACF,cAAc;;;;;;;AAOzD,EAAED,kBAAkBC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;AAyBlC,CAAC;AACD;AAEA,SAAS0B,2BAA2B1B,YAAoB,EAAE2B,IAAY;IACpE,IAAIC,uBAAuB;IAE3B,IACEX,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBQ,KAAKE,QAAQ,CAAC,sBACd;QACAD,uBAAuB,CAAC;;;;;;;;;;IAUxB,CAAC;IACH;IAEA,MAAMR,OAAO,CAAC;;0BAEU,EAAEnB,KAAKC,SAAS,CAACF,cAAc;;;;;;oBAMrC,EAAEC,KAAKC,SAAS,CAACW,eAAeb,eAAe;iBAClD,EAAEC,KAAKC,SAAS,CAACK,wBAAwBP,cAAcU,IAAI,EAAE;;AAE9E,EAAEX,kBAAkBC,cAAc;;AAElC,EAAE,GAAG,wCAAwC,IAAG;cAClC,EAAEC,KAAKC,SAAS,CAACF,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA8BtB,EAAEC,KAAKC,SAAS,CAACC,YAAYG,UAAU,EAAE;;;;;AAKhE,EAAEsB,qBAAqB;AACvB,CAAC;IACC,OAAOR;AACT;AACA,gEAAgE;AAChE,gFAAgF;AAChF,oDAAoD;AACpD,MAAMU,2BACJ;IACE,MAAM,EAAE9B,YAAY,EAAE,GAAG,IAAI;IAC7B,MAAM,EAAE2B,IAAI,EAAEI,SAAS,EAAE,GAAG,IAAI,CAACC,UAAU;IAC3C,MAAM,EAAEtB,MAAMK,YAAY,EAAE,GAAGR,wBAAwBP;IAEvD,IAAIoB,OAAO;IACX,IAAIW,cAAc,KAAK;QACrB,IAAIhB,iBAAiB,YAAYA,iBAAiB,YAAY;YAC5DK,OAAOI,wBAAwBxB;QACjC,OAAO,IAAIe,iBAAiB,WAAW;YACrCK,OAAOM,2BAA2B1B,cAAc2B;QAClD,OAAO;YACLP,OAAOK,yBAAyBzB;QAClC;IACF,OAAO;QACLoB,OAAO,MAAMN,wBAAwBd,cAAce;IACrD;IAEA,OAAOK;AACT;AAEF,eAAeU,yBAAwB"}