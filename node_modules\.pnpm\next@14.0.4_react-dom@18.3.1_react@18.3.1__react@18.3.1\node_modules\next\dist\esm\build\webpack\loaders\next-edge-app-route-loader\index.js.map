{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-edge-app-route-loader/index.ts"], "names": ["getModuleBuildInfo", "stringifyRequest", "WEBPACK_RESOURCE_QUERIES", "loadEntrypoint", "EdgeAppRouteLoader", "page", "absolutePagePath", "preferredRegion", "appDirLoader", "appDirLoaderBase64", "middlewareConfig", "middlewareConfigBase64", "getOptions", "<PERSON><PERSON><PERSON>", "from", "toString", "JSON", "parse", "_module", "Error", "buildInfo", "nextEdgeSSR", "isServerComponent", "isAppDir", "route", "stringifiedPagePath", "modulePath", "substring", "length", "edgeSSREntry", "VAR_USERLAND"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,2BAA0B;AAC7D,SAASC,gBAAgB,QAAQ,0BAAyB;AAG1D,SAASC,wBAAwB,QAAQ,4BAA2B;AAEpE,SAASC,cAAc,QAAQ,2BAA0B;AAWzD,MAAMC,qBACJ;IACE,MAAM,EACJC,IAAI,EACJC,gBAAgB,EAChBC,eAAe,EACfC,cAAcC,qBAAqB,EAAE,EACrCC,kBAAkBC,yBAAyB,EAAE,EAC9C,GAAG,IAAI,CAACC,UAAU;IAEnB,MAAMJ,eAAeK,OAAOC,IAAI,CAACL,oBAAoB,UAAUM,QAAQ;IACvE,MAAML,mBAAqCM,KAAKC,KAAK,CACnDJ,OAAOC,IAAI,CAACH,wBAAwB,UAAUI,QAAQ;IAGxD,kDAAkD;IAClD,IAAI,CAAC,IAAI,CAACG,OAAO,EAAE,MAAM,IAAIC,MAAM;IAEnC,MAAMC,YAAYpB,mBAAmB,IAAI,CAACkB,OAAO;IAEjDE,UAAUC,WAAW,GAAG;QACtBC,mBAAmB;QACnBjB,MAAMA;QACNkB,UAAU;IACZ;IACAH,UAAUI,KAAK,GAAG;QAChBnB;QACAC;QACAC;QACAG;IACF;IAEA,MAAMe,sBAAsBxB,iBAAiB,IAAI,EAAEK;IACnD,MAAMoB,aAAa,CAAC,EAAElB,aAAa,EAAEiB,oBAAoBE,SAAS,CAChE,GACAF,oBAAoBG,MAAM,GAAG,GAC7B,CAAC,EAAE1B,yBAAyB2B,YAAY,CAAC,CAAC;IAE5C,OAAO,MAAM1B,eAAe,kBAAkB;QAC5C2B,cAAcJ;IAChB;AACF;AAEF,eAAetB,mBAAkB"}