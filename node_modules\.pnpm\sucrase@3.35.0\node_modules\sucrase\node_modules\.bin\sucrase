#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Projects/augment_code_v0_compare/node_modules/.pnpm/sucrase@3.35.0/node_modules/sucrase/bin/node_modules:/mnt/c/Projects/augment_code_v0_compare/node_modules/.pnpm/sucrase@3.35.0/node_modules/sucrase/node_modules:/mnt/c/Projects/augment_code_v0_compare/node_modules/.pnpm/sucrase@3.35.0/node_modules:/mnt/c/Projects/augment_code_v0_compare/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Projects/augment_code_v0_compare/node_modules/.pnpm/sucrase@3.35.0/node_modules/sucrase/bin/node_modules:/mnt/c/Projects/augment_code_v0_compare/node_modules/.pnpm/sucrase@3.35.0/node_modules/sucrase/node_modules:/mnt/c/Projects/augment_code_v0_compare/node_modules/.pnpm/sucrase@3.35.0/node_modules:/mnt/c/Projects/augment_code_v0_compare/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/sucrase" "$@"
else
  exec node  "$basedir/../../bin/sucrase" "$@"
fi
