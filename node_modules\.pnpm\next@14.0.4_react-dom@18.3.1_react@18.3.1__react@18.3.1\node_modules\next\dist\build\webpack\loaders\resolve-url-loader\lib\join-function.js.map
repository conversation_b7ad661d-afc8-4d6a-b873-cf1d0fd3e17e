{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/resolve-url-loader/lib/join-function.ts"], "names": ["defaultJoin", "compose", "f", "g", "args", "simpleJoin", "path", "normalize", "join", "createJoinForPredicate", "predicate", "_", "uri", "base", "i", "next", "absolute", "fs", "existsSync", "createIterator", "arr", "name", "filename", "options", "log", "createDebugLogger", "debug", "joinProper", "baseOrIteratorOrAbsent", "iterator", "root", "result", "runIterator", "createJoin<PERSON>g", "isFound", "accumulator", "nextItem", "done", "value", "element", "length", "isAbsolute", "Object", "assign", "concat", "Array", "isArray", "Error", "fallback", "toString", "valueOf", "file", "bases", "pathToString", "map", "filter", "Boolean", "relative", "process", "cwd", "split", "sep", "exports", "console", "cache", "<PERSON><PERSON><PERSON>", "noop", "msgFn", "params", "key", "JSON", "stringify", "apply"], "mappings": "AAAA,uEAAuE;AACvE,0DAA0D,GAC1D;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;;+BAkBaA;;;eAAAA;;;6DAjBI;2DACF;;;;;;AAEf,MAAMC,UACJ,CAACC,GAAQC,IACT,CAAC,GAAGC,OACFF,EAAEC,KAAKC;AAEX,MAAMC,aAAaJ,QAAQK,aAAI,CAACC,SAAS,EAAED,aAAI,CAACE,IAAI;AAS7C,MAAMR,cAAcS,uBAAuB,SAASC,UACzDC,CAAM,EACNC,GAAQ,EACRC,IAAS,EACTC,CAAM,EACNC,IAAS;IAET,MAAMC,WAAWX,WAAWQ,MAAMD;IAClC,OAAOK,WAAE,CAACC,UAAU,CAACF,YAAYA,WAAWD,KAAKD,MAAM,IAAIE,WAAW;AACxE,GACA;AAEA,UAAUG,eAAeC,GAAQ;IAC/B,KAAK,MAAMN,KAAKM,IAAK;QACnB,MAAMN;IACR;AACF;AAEA;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,SAASL,uBACP,2CAA2C,GAC3CC,SAAc,EACd,kDAAkD,GAClDW,IAAY;IAEZ;;GAEC,GACD,SAASb,KACP,qCAAqC,GACrCc,QAAgB,EAChB,oBAAoB,GACpBC,OAAgD;QAEhD,MAAMC,MAAMC,kBAAkBF,QAAQG,KAAK;QAE3C;;;;;;KAMC,GACD,OAAO,SAASC,WACd,qCAAqC,GACrCf,GAAW,EACX,oDAAoD,GACpDgB,sBAA2B;YAE3B,MAAMC,WACJ,AAAC,OAAOD,2BAA2B,eACjCT,eAAe;gBAACI,QAAQO,IAAI;aAAC,KAC9B,OAAOF,2BAA2B,YACjCT,eAAe;gBAACS;aAAuB,KACzCA;YAEF,MAAMG,SAASC,YAAY,EAAE;YAC7BR,IAAIS,eAAe;gBAACX;gBAAUV;gBAAKmB;gBAAQA,OAAOG,OAAO;aAAC;YAE1D,OAAO,OAAOH,OAAOf,QAAQ,KAAK,WAAWe,OAAOf,QAAQ,GAAGJ;YAE/D,SAASoB,YAAYG,WAAgB;gBACnC,MAAMC,WAAWP,SAASd,IAAI;gBAC9B,IAAIF,OAAO,CAACuB,SAASC,IAAI,IAAID,SAASE,KAAK;gBAC3C,IAAI,OAAOzB,SAAS,UAAU;oBAC5B,MAAM0B,UAAU7B,UACdY,UACAV,KACAC,MACAsB,YAAYK,MAAM,EAClBzB;oBAGF,IAAI,OAAOwB,YAAY,YAAYjC,aAAI,CAACmC,UAAU,CAACF,UAAU;wBAC3D,OAAOG,OAAOC,MAAM,CAACR,YAAYS,MAAM,CAAC/B,OAAO;4BAC7CqB,SAAS;4BACTlB,UAAUuB;wBACZ;oBACF,OAAO,IAAIM,MAAMC,OAAO,CAACP,UAAU;wBACjC,OAAOA;oBACT,OAAO;wBACL,MAAM,IAAIQ,MACR;oBAEJ;gBACF,OAAO;oBACL,OAAOZ;gBACT;gBAEA,SAASpB,KAAKiC,QAAa;oBACzB,OAAOhB,YACLU,OAAOC,MAAM,CACXR,YAAYS,MAAM,CAAC/B,OACnB,OAAOmC,aAAa,YAAY;wBAAEhC,UAAUgC;oBAAS;gBAG3D;YACF;QACF;IACF;IAEA,SAASC;QACP,OAAO,gBAAgB5B,OAAO;IAChC;IAEA,OAAOqB,OAAOC,MAAM,CAClBnC,MACAa,QAAQ;QACN6B,SAASD;QACTA,UAAUA;IACZ;AAEJ;AAEA;;;CAGC,GACD,SAAShB,cACP,wCAAwC,GACxCkB,IAAY,EACZ,sCAAsC,GACtCvC,GAAW,EACX,0DAA0D,GAC1DwC,KAAe,EACf,wCAAwC,GACxClB,OAAgB;IAEhB,OAAO;QACL,yBAAyBmB,aAAaF,QAAQ,OAAOvC;QACrD,EAAE;WACCwC,MAAME,GAAG,CAACD,cAAcE,MAAM,CAACC;WAC9BtB,UAAU;YAAC;SAAQ,GAAG;YAAC;SAAY;KACxC,CAAC1B,IAAI,CAAC;IAEP;;;;GAIC,GACD,SAAS6C,aACP,qBAAqB,GACrBrC,QAAgB;QAEhB,IAAI,CAACA,UAAU;YACb,OAAO;QACT,OAAO;YACL,MAAMyC,WAAWnD,aAAI,CAACmD,QAAQ,CAACC,QAAQC,GAAG,IAAI3C,UAAU4C,KAAK,CAACtD,aAAI,CAACuD,GAAG;YAEtE,OAAO,AACLJ,CAAAA,QAAQ,CAAC,EAAE,KAAK,OACZzC,SAAS4C,KAAK,CAACtD,aAAI,CAACuD,GAAG,IACvB;gBAAC;aAAI,CAACjB,MAAM,CAACa,UAAUF,MAAM,CAACC,QAAO,EACzChD,IAAI,CAAC;QACT;IACF;AACF;AAEAsD,QAAQ7B,aAAa,GAAGA;AAExB;;;;;;;;;;CAUC,GACD,SAASR,kBACP,gCAAgC,GAChCC,KAAoB;IAEpB,MAAMF,MAAM,CAAC,CAACE,SAAU,CAAA,OAAOA,UAAU,aAAaA,QAAQqC,QAAQvC,GAAG,AAAD;IACxE,MAAMwC,QAAa,CAAC;IACpB,OAAOxC,MAAMyC,cAAcC;IAE3B,SAASA,QAAQ;IAEjB,SAASD,YAAYE,KAAU,EAAEC,MAAW;QAC1C,MAAMC,MAAMC,KAAKC,SAAS,CAACH;QAC3B,IAAI,CAACJ,KAAK,CAACK,IAAI,EAAE;YACfL,KAAK,CAACK,IAAI,GAAG;YACb7C,IAAI2C,MAAMK,KAAK,CAAC,MAAMJ;QACxB;IACF;AACF;AAEAN,QAAQrC,iBAAiB,GAAGA"}