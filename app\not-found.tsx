'use client';

import Link from 'next/link';
import { Layout } from '@/components/Layout';
import { Home, ArrowLeft } from 'lucide-react';

export default function NotFound() {
  return (
    <Layout>
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <div className="mb-8">
            <h1 className="text-6xl font-bold text-primary-300 mb-4">404</h1>
            <h2 className="text-2xl font-semibold text-primary-900 mb-4">
              Page Not Found
            </h2>
            <p className="text-primary-600 leading-relaxed">
              The page you're looking for doesn't exist or has been moved to a different location.
            </p>
          </div>

          <div className="space-y-4">
            <Link 
              href="/"
              className="btn-primary inline-flex items-center space-x-2"
            >
              <Home size={16} />
              <span>Go Home</span>
            </Link>
            
            <button 
              onClick={() => window.history.back()}
              className="btn-secondary inline-flex items-center space-x-2 ml-4"
            >
              <ArrowLeft size={16} />
              <span>Go Back</span>
            </button>
          </div>
        </div>
      </div>
    </Layout>
  );
}
