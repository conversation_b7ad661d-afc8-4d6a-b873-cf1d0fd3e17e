{"version": 3, "sources": ["../../../../../src/build/webpack/config/blocks/base.ts"], "names": ["curry", "COMPILER_NAMES", "base", "ctx", "config", "mode", "isDevelopment", "name", "isServer", "isEdgeRuntime", "edgeServer", "server", "client", "target", "targetWeb", "process", "env", "__NEXT_TEST_MODE", "__NEXT_TEST_WITH_DEVTOOL", "devtool", "serverSourceMaps", "productionBrowserSourceMaps", "isClient", "module", "rules"], "mappings": "AAAA,OAAOA,WAAW,kCAAiC;AAEnD,SAASC,cAAc,QAAQ,mCAAkC;AAGjE,OAAO,MAAMC,OAAOF,MAAM,SAASE,KACjCC,GAAyB,EACzBC,MAA6B;IAE7BA,OAAOC,IAAI,GAAGF,IAAIG,aAAa,GAAG,gBAAgB;IAClDF,OAAOG,IAAI,GAAGJ,IAAIK,QAAQ,GACtBL,IAAIM,aAAa,GACfR,eAAeS,UAAU,GACzBT,eAAeU,MAAM,GACvBV,eAAeW,MAAM;IAEzBR,OAAOS,MAAM,GAAG,CAACV,IAAIW,SAAS,GAC1B,YAAY,6DAA6D;OACzEX,IAAIM,aAAa,GACjB;QAAC;QAAO;KAAM,GACd;QAAC;QAAO;KAAM;IAElB,4DAA4D;IAC5D,IAAIN,IAAIG,aAAa,EAAE;QACrB,IAAIS,QAAQC,GAAG,CAACC,gBAAgB,IAAI,CAACF,QAAQC,GAAG,CAACE,wBAAwB,EAAE;YACzEd,OAAOe,OAAO,GAAG;QACnB,OAAO;YACL,+DAA+D;YAC/D,kEAAkE;YAClE,mEAAmE;YACnE,uDAAuD;YACvDf,OAAOe,OAAO,GAAG;QACnB;IACF,OAAO;QACL,IACEhB,IAAIM,aAAa,IAChBN,IAAIK,QAAQ,IAAIL,IAAIiB,gBAAgB,IACrC,6BAA6B;QAC5BjB,IAAIkB,2BAA2B,IAAIlB,IAAImB,QAAQ,EAChD;YACAlB,OAAOe,OAAO,GAAG;QACnB,OAAO;YACLf,OAAOe,OAAO,GAAG;QACnB;IACF;IAEA,IAAI,CAACf,OAAOmB,MAAM,EAAE;QAClBnB,OAAOmB,MAAM,GAAG;YAAEC,OAAO,EAAE;QAAC;IAC9B;IAEA,6EAA6E;IAC7E,mDAAmD;IAEnD,OAAOpB;AACT,GAAE"}