"use strict";(()=>{var e={};e.id=165,e.ids=[165],e.modules={7849:e=>{e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6023:(e,t,r)=>{r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>l,routeModule:()=>x,tree:()=>d});var n=r(6625),o=r(5345),a=r(4666),s=r.n(a),i=r(8889),p={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>i[e]);r.d(t,p);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1737)),"C:\\Projects\\augment_code_v0_compare\\app\\not-found.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,6013)),"C:\\Projects\\augment_code_v0_compare\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,8363)),"C:\\Projects\\augment_code_v0_compare\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,1737)),"C:\\Projects\\augment_code_v0_compare\\app\\not-found.tsx"]}],l=[],c="/_not-found",u={require:r,loadChunk:()=>Promise.resolve()},x=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[863,543],()=>r(6023));module.exports=n})();