I provided v0.app the following to mockup a website. Read the requirements and implement the next.js website.

Website Requirements

Name of website: St Cloud Enterprises Portal
Website supports responsive design principles as managed by the Tailwind CSS framework
Website uses the Tailwind Zinc color with steps as a temporary color palette
Website color is configurable to any of the Tailwind colors
Website manages access to other website properties managed by St Cloud Enterprises
Website footer shows the name of the website as well as a description of its mission statement
Website footer shows options for Privacy Policy and Terms of Service, content formatting compatible with markdown
Website shows its content in a format with able white-space to declutter the presentation
Website has dedicate pages for following: Page not found, Unauthorized access, and errors
Website shows a loading indicator specific to pages whenever the navigation takes more than 1 sec
Website shows a loading indicator specific to actions whenever the action takes more than 500 msec
Home page is a simple view showing the name of website in large text, centered horizontally and vertically at 1/3 from top, that's a hyperlink to the Dashboard which is only accessible to authenticated users
Anonymous users are directed to the Sign In page to access restricted pages: Account and Dashboard
Dashboard page shows a grid layout of properties in card format with the name of website in the card header, image and description in the card middle, and the date the website was established right aligned in the card footer
Once a user is authenticated, each page shows options for accessing the following: Account, Dashboard, and Sign Out
Each page shows options for accessing other website pages
Each page shows own title
Font is Geist from Vercel
