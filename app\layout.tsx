import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON>st<PERSON><PERSON>, GeistMono } from 'geist/font';
import './globals.css';
import { AuthProvider } from '@/contexts/AuthContext';
import { siteConfig } from '@/lib/config';

export const metadata: Metadata = {
  title: siteConfig.name,
  description: siteConfig.description,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${GeistSans.variable} ${GeistMono.variable}`}>
      <body>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
