/*
 React
 react-dom-server.browser.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.


 JS Implementation of MurmurHash3 (r136) (as of May 20, 2011)

 Copyright (c) 2011 Gary Court
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 SOFTWARE.
*/
'use strict';var aa=require("next/dist/compiled/react-experimental"),da=require("react-dom");function l(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,c=1;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c]);return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}
function ea(a,b){var c=a.length&3;var d=a.length-c;var e=b;for(b=0;b<d;){var f=a.charCodeAt(b)&255|(a.charCodeAt(++b)&255)<<8|(a.charCodeAt(++b)&255)<<16|(a.charCodeAt(++b)&255)<<24;++b;f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295;f=f<<15|f>>>17;f=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295;e^=f;e=e<<13|e>>>19;e=5*(e&65535)+((5*(e>>>16)&65535)<<16)&4294967295;e=(e&65535)+27492+(((e>>>16)+58964&65535)<<16)}f=0;switch(c){case 3:f^=(a.charCodeAt(b+2)&255)<<
16;case 2:f^=(a.charCodeAt(b+1)&255)<<8;case 1:f^=a.charCodeAt(b)&255,f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295,f=f<<15|f>>>17,e^=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295}e^=a.length;e^=e>>>16;e=2246822507*(e&65535)+((2246822507*(e>>>16)&65535)<<16)&4294967295;e^=e>>>13;e=3266489909*(e&65535)+((3266489909*(e>>>16)&65535)<<16)&4294967295;return(e^e>>>16)>>>0}var n=null,r=0;
function v(a,b){if(0!==b.byteLength)if(512<b.byteLength)0<r&&(a.enqueue(new Uint8Array(n.buffer,0,r)),n=new Uint8Array(512),r=0),a.enqueue(b);else{var c=n.length-r;c<b.byteLength&&(0===c?a.enqueue(n):(n.set(b.subarray(0,c),r),a.enqueue(n),b=b.subarray(c)),n=new Uint8Array(512),r=0);n.set(b,r);r+=b.byteLength}}function w(a,b){v(a,b);return!0}function fa(a){n&&0<r&&(a.enqueue(new Uint8Array(n.buffer,0,r)),n=null,r=0)}var ha=new TextEncoder;function z(a){return ha.encode(a)}
function B(a){return ha.encode(a)}function ka(a,b){"function"===typeof a.error?a.error(b):a.close()}
var C=Object.assign,E=Object.prototype.hasOwnProperty,la=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),pa={},qa={};
function ra(a){if(E.call(qa,a))return!0;if(E.call(pa,a))return!1;if(la.test(a))return qa[a]=!0;pa[a]=!0;return!1}
var ya=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),za=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Aa=/["'&<>]/;
function H(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Aa.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Ha=/([A-Z])/g,Ia=/^ms-/,Ja=Array.isArray,Ka=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,La={pending:!1,data:null,method:null,action:null},Ma=da.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,ab={prefetchDNS:Na,preconnect:Oa,preload:Wa,preloadModule:Xa,preinitStyle:Ya,preinitScript:Za,preinitModuleScript:$a},J=[],bb=B('"></template>'),cb=B("<script>"),db=B("\x3c/script>"),rb=B('<script src="'),sb=B('<script type="module" src="'),tb=B('" nonce="'),ub=B('" integrity="'),
vb=B('" crossorigin="'),wb=B('" async="">\x3c/script>'),xb=/(<\/|<)(s)(cript)/gi;function yb(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}var zb=B('<script type="importmap">'),Ab=B("\x3c/script>");
function Jb(a,b,c,d,e,f){var g=void 0===b?cb:B('<script nonce="'+H(b)+'">'),h=a.idPrefix,k=[],m=null,p=a.bootstrapScriptContent,t=a.bootstrapScripts,q=a.bootstrapModules;void 0!==p&&k.push(g,z((""+p).replace(xb,yb)),db);void 0!==c&&("string"===typeof c?(m={src:c,chunks:[]},Kb(m.chunks,{src:c,async:!0,integrity:void 0,nonce:b})):(m={src:c.src,chunks:[]},Kb(m.chunks,{src:c.src,async:!0,integrity:c.integrity,nonce:b})));c=[];void 0!==d&&(c.push(zb),c.push(z((""+JSON.stringify(d)).replace(xb,yb))),c.push(Ab));
d=e?{preconnects:"",fontPreloads:"",highImagePreloads:"",remainingCapacity:"number"===typeof f?f:2E3}:null;e={placeholderPrefix:B(h+"P:"),segmentPrefix:B(h+"S:"),boundaryPrefix:B(h+"B:"),startInlineScript:g,htmlChunks:null,headChunks:null,externalRuntimeScript:m,bootstrapChunks:k,onHeaders:e,headers:d,resets:{font:{},dns:{},connect:{default:{},anonymous:{},credentials:{}},image:{},style:{}},charsetChunks:[],preconnectChunks:[],importMapChunks:c,preloadChunks:[],hoistableChunks:[],preconnects:new Set,
fontPreloads:new Set,highImagePreloads:new Set,styles:new Map,bootstrapScripts:new Set,scripts:new Set,bulkPreloads:new Set,preloads:{images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map},nonce:b,boundaryResources:null,stylesToHoist:!1};if(void 0!==t)for(g=0;g<t.length;g++)c=t[g],d=m=void 0,f={rel:"preload",as:"script",fetchPriority:"low",nonce:b},"string"===typeof c?f.href=h=c:(f.href=h=c.src,f.integrity=d="string"===typeof c.integrity?c.integrity:void 0,f.crossOrigin=m="string"===
typeof c||null==c.crossOrigin?void 0:"use-credentials"===c.crossOrigin?"use-credentials":""),c=a,p=h,c.scriptResources[p]=null,c.moduleScriptResources[p]=null,c=[],K(c,f),e.bootstrapScripts.add(c),k.push(rb,z(H(h))),b&&k.push(tb,z(H(b))),"string"===typeof d&&k.push(ub,z(H(d))),"string"===typeof m&&k.push(vb,z(H(m))),k.push(wb);if(void 0!==q)for(t=0;t<q.length;t++)f=q[t],m=h=void 0,d={rel:"modulepreload",fetchPriority:"low",nonce:b},"string"===typeof f?d.href=g=f:(d.href=g=f.src,d.integrity=m="string"===
typeof f.integrity?f.integrity:void 0,d.crossOrigin=h="string"===typeof f||null==f.crossOrigin?void 0:"use-credentials"===f.crossOrigin?"use-credentials":""),f=a,c=g,f.scriptResources[c]=null,f.moduleScriptResources[c]=null,f=[],K(f,d),e.bootstrapScripts.add(f),k.push(sb,z(H(g))),b&&k.push(tb,z(H(b))),"string"===typeof m&&k.push(ub,z(H(m))),"string"===typeof h&&k.push(vb,z(H(h))),k.push(wb);return e}
function Lb(a,b,c,d,e){var f=0;void 0!==b&&(f=1);return{idPrefix:void 0===a?"":a,nextFormID:0,streamingFormat:f,bootstrapScriptContent:c,bootstrapScripts:d,bootstrapModules:e,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}}}function O(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function Mb(a){return O("http://www.w3.org/2000/svg"===a?3:"http://www.w3.org/1998/Math/MathML"===a?4:0,null,0)}
function Nb(a,b,c){switch(b){case "noscript":return O(2,null,a.tagScope|1);case "select":return O(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return O(3,null,a.tagScope);case "picture":return O(2,null,a.tagScope|2);case "math":return O(4,null,a.tagScope);case "foreignObject":return O(2,null,a.tagScope);case "table":return O(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return O(6,null,a.tagScope);case "colgroup":return O(8,null,a.tagScope);case "tr":return O(7,null,a.tagScope)}return 5<=
a.insertionMode?O(2,null,a.tagScope):0===a.insertionMode?"html"===b?O(1,null,a.tagScope):O(2,null,a.tagScope):1===a.insertionMode?O(2,null,a.tagScope):a}var Ob=B("\x3c!-- --\x3e");function Pb(a,b,c,d){if(""===b)return d;d&&a.push(Ob);a.push(z(H(b)));return!0}var Qb=new Map,Rb=B(' style="'),Sb=B(":"),Tb=B(";");
function Ub(a,b){if("object"!==typeof b)throw Error(l(62));var c=!0,d;for(d in b)if(E.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=z(H(d));e=z(H((""+e).trim()))}else f=Qb.get(d),void 0===f&&(f=B(H(d.replace(Ha,"-$1").toLowerCase().replace(Ia,"-ms-"))),Qb.set(d,f)),e="number"===typeof e?0===e||ya.has(d)?z(""+e):z(e+"px"):z(H((""+e).trim()));c?(c=!1,a.push(Rb,f,Sb,e)):a.push(Tb,f,Sb,e)}}c||a.push(P)}var Q=B(" "),Vb=B('="'),P=B('"'),Wb=B('=""');
function Xb(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,z(b),Wb)}function R(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(Q,z(b),Vb,z(H(c)),P)}function Yb(a){var b=a.nextFormID++;return a.idPrefix+b}var Zb=B(H("javascript:throw new Error('A React form was unexpectedly submitted.')")),$b=B('<input type="hidden"');function ac(a,b){this.push($b);if("string"!==typeof a)throw Error(l(480));R(this,"name",b);R(this,"value",a);this.push(bc)}
function cc(a,b,c,d,e,f,g,h){var k=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=Yb(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,k=b.data):(a.push(Q,z("formAction"),Vb,Zb,P),g=f=e=d=h=null,dc(b,c)));null!=h&&S(a,"name",h);null!=d&&S(a,"formAction",d);null!=e&&S(a,"formEncType",e);null!=f&&S(a,"formMethod",f);null!=g&&S(a,"formTarget",g);return k}
function S(a,b,c){switch(b){case "className":R(a,"class",c);break;case "tabIndex":R(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":R(a,b,c);break;case "style":Ub(a,c);break;case "src":case "href":if(""===c)break;case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;c=""+c;a.push(Q,z(b),Vb,z(H(c)),P);break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":Xb(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;c=""+c;a.push(Q,z("xlink:href"),Vb,z(H(c)),P);break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,z(b),Vb,z(H(c)),P);break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,z(b),Wb);break;case "capture":case "download":!0===c?a.push(Q,z(b),Wb):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,z(b),Vb,z(H(c)),P);break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(Q,z(b),Vb,z(H(c)),P);break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(Q,z(b),Vb,z(H(c)),P);break;case "xlinkActuate":R(a,"xlink:actuate",
c);break;case "xlinkArcrole":R(a,"xlink:arcrole",c);break;case "xlinkRole":R(a,"xlink:role",c);break;case "xlinkShow":R(a,"xlink:show",c);break;case "xlinkTitle":R(a,"xlink:title",c);break;case "xlinkType":R(a,"xlink:type",c);break;case "xmlBase":R(a,"xml:base",c);break;case "xmlLang":R(a,"xml:lang",c);break;case "xmlSpace":R(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=za.get(b)||b,ra(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(Q,z(b),Vb,z(H(c)),P)}}}var U=B(">"),bc=B("/>");function ec(a,b,c){if(null!=b){if(null!=c)throw Error(l(60));if("object"!==typeof b||!("__html"in b))throw Error(l(61));b=b.__html;null!==b&&void 0!==b&&a.push(z(""+b))}}function fc(a){var b="";aa.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}var gc=B(' selected=""'),hc=B('addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'A React form was unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.getRootNode(),(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,\nd,b))}});');
function dc(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,hc,db))}var ic=B("\x3c!--F!--\x3e"),jc=B("\x3c!--F--\x3e");
function kc(a,b,c,d,e,f,g){var h=b.rel,k=b.href,m=b.precedence;if(3===f||g||null!=b.itemProp||"string"!==typeof h||"string"!==typeof k||""===k)return K(a,b),null;if("stylesheet"===b.rel){if("string"!==typeof m||null!=b.disabled||b.onLoad||b.onError)return K(a,b);f=d.styles.get(m);g=c.styleResources.hasOwnProperty(k)?c.styleResources[k]:void 0;null!==g?(c.styleResources[k]=null,f||(f={precedence:z(H(m)),rules:[],hrefs:[],sheets:new Map},d.styles.set(m,f)),b={state:0,props:C({},b,{"data-precedence":b.precedence,
precedence:null})},g&&(2===g.length&&lc(b.props,g),(c=d.preloads.stylesheets.get(k))&&0<c.length?c.length=0:b.state=1),f.sheets.set(k,b),d.boundaryResources&&d.boundaryResources.stylesheets.add(b)):f&&(k=f.sheets.get(k))&&d.boundaryResources&&d.boundaryResources.stylesheets.add(k);e&&a.push(Ob);return null}if(b.onLoad||b.onError)return K(a,b);e&&a.push(Ob);switch(b.rel){case "preconnect":case "dns-prefetch":return K(d.preconnectChunks,b);case "preload":return K(d.preloadChunks,b);default:return K(d.hoistableChunks,
b)}}function K(a,b){a.push(V("link"));for(var c in b)if(E.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error(l(399,"link"));default:S(a,c,d)}}a.push(bc);return null}function mc(a,b,c){a.push(V(c));for(var d in b)if(E.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(l(399,c));default:S(a,d,e)}}a.push(bc);return null}
function nc(a,b){a.push(V("title"));var c=null,d=null,e;for(e in b)if(E.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:S(a,e,f)}}a.push(U);b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(z(H(""+b)));ec(a,d,c);a.push(oc("title"));return null}
function Kb(a,b){a.push(V("script"));var c=null,d=null,e;for(e in b)if(E.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:S(a,e,f)}}a.push(U);ec(a,d,c);"string"===typeof c&&a.push(z(H(c)));a.push(oc("script"));return null}
function pc(a,b,c){a.push(V(c));var d=c=null,e;for(e in b)if(E.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:S(a,e,f)}}a.push(U);ec(a,d,c);return"string"===typeof c?(a.push(z(H(c))),null):c}var zc=B("\n"),Ac=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Bc=new Map;function V(a){var b=Bc.get(a);if(void 0===b){if(!Ac.test(a))throw Error(l(65,a));b=B("<"+a);Bc.set(a,b)}return b}var Cc=B("<!DOCTYPE html>");
function Dc(a,b,c,d,e,f,g){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(V("select"));var h=null,k=null,m;for(m in c)if(E.call(c,m)){var p=c[m];if(null!=p)switch(m){case "children":h=p;break;case "dangerouslySetInnerHTML":k=p;break;case "defaultValue":case "value":break;default:S(a,m,p)}}a.push(U);ec(a,k,h);return h;case "option":var t=f.selectedValue;a.push(V("option"));var q=null,x=null,A=null,X=null,u;for(u in c)if(E.call(c,
u)){var D=c[u];if(null!=D)switch(u){case "children":q=D;break;case "selected":A=D;break;case "dangerouslySetInnerHTML":X=D;break;case "value":x=D;default:S(a,u,D)}}if(null!=t){var F=null!==x?""+x:fc(q);if(Ja(t))for(var ma=0;ma<t.length;ma++){if(""+t[ma]===F){a.push(gc);break}}else""+t===F&&a.push(gc)}else A&&a.push(gc);a.push(U);ec(a,X,q);return q;case "textarea":a.push(V("textarea"));var y=null,T=null,G=null,L;for(L in c)if(E.call(c,L)){var M=c[L];if(null!=M)switch(L){case "children":G=M;break;case "value":y=
M;break;case "defaultValue":T=M;break;case "dangerouslySetInnerHTML":throw Error(l(91));default:S(a,L,M)}}null===y&&null!==T&&(y=T);a.push(U);if(null!=G){if(null!=y)throw Error(l(92));if(Ja(G)){if(1<G.length)throw Error(l(93));y=""+G[0]}y=""+G}"string"===typeof y&&"\n"===y[0]&&a.push(zc);null!==y&&a.push(z(H(""+y)));return null;case "input":a.push(V("input"));var Pa=null,Ba=null,sa=null,na=null,Ca=null,ta=null,ua=null,va=null,Qa=null,ia;for(ia in c)if(E.call(c,ia)){var ba=c[ia];if(null!=ba)switch(ia){case "children":case "dangerouslySetInnerHTML":throw Error(l(399,
"input"));case "name":Pa=ba;break;case "formAction":Ba=ba;break;case "formEncType":sa=ba;break;case "formMethod":na=ba;break;case "formTarget":Ca=ba;break;case "defaultChecked":Qa=ba;break;case "defaultValue":ua=ba;break;case "checked":va=ba;break;case "value":ta=ba;break;default:S(a,ia,ba)}}var Cd=cc(a,d,e,Ba,sa,na,Ca,Pa);null!==va?Xb(a,"checked",va):null!==Qa&&Xb(a,"checked",Qa);null!==ta?S(a,"value",ta):null!==ua&&S(a,"value",ua);a.push(bc);null!==Cd&&Cd.forEach(ac,a);return null;case "button":a.push(V("button"));
var eb=null,Dd=null,Ed=null,Fd=null,Gd=null,Hd=null,Id=null,fb;for(fb in c)if(E.call(c,fb)){var oa=c[fb];if(null!=oa)switch(fb){case "children":eb=oa;break;case "dangerouslySetInnerHTML":Dd=oa;break;case "name":Ed=oa;break;case "formAction":Fd=oa;break;case "formEncType":Gd=oa;break;case "formMethod":Hd=oa;break;case "formTarget":Id=oa;break;default:S(a,fb,oa)}}var Jd=cc(a,d,e,Fd,Gd,Hd,Id,Ed);a.push(U);null!==Jd&&Jd.forEach(ac,a);ec(a,Dd,eb);if("string"===typeof eb){a.push(z(H(eb)));var Kd=null}else Kd=
eb;return Kd;case "form":a.push(V("form"));var gb=null,Ld=null,wa=null,hb=null,ib=null,jb=null,kb;for(kb in c)if(E.call(c,kb)){var xa=c[kb];if(null!=xa)switch(kb){case "children":gb=xa;break;case "dangerouslySetInnerHTML":Ld=xa;break;case "action":wa=xa;break;case "encType":hb=xa;break;case "method":ib=xa;break;case "target":jb=xa;break;default:S(a,kb,xa)}}var qc=null,rc=null;if("function"===typeof wa)if("function"===typeof wa.$$FORM_ACTION){var wf=Yb(d),Ra=wa.$$FORM_ACTION(wf);wa=Ra.action||"";hb=
Ra.encType;ib=Ra.method;jb=Ra.target;qc=Ra.data;rc=Ra.name}else a.push(Q,z("action"),Vb,Zb,P),jb=ib=hb=wa=null,dc(d,e);null!=wa&&S(a,"action",wa);null!=hb&&S(a,"encType",hb);null!=ib&&S(a,"method",ib);null!=jb&&S(a,"target",jb);a.push(U);null!==rc&&(a.push($b),R(a,"name",rc),a.push(bc),null!==qc&&qc.forEach(ac,a));ec(a,Ld,gb);if("string"===typeof gb){a.push(z(H(gb)));var Md=null}else Md=gb;return Md;case "menuitem":a.push(V("menuitem"));for(var Bb in c)if(E.call(c,Bb)){var Nd=c[Bb];if(null!=Nd)switch(Bb){case "children":case "dangerouslySetInnerHTML":throw Error(l(400));
default:S(a,Bb,Nd)}}a.push(U);return null;case "title":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Od=nc(a,c);else nc(e.hoistableChunks,c),Od=null;return Od;case "link":return kc(a,c,d,e,g,f.insertionMode,!!(f.tagScope&1));case "script":var sc=c.async;if("string"!==typeof c.src||!c.src||!sc||"function"===typeof sc||"symbol"===typeof sc||c.onLoad||c.onError||3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Pd=Kb(a,c);else{var Cb=c.src;if("module"===c.type){var Db=d.moduleScriptResources;
var Qd=e.preloads.moduleScripts}else Db=d.scriptResources,Qd=e.preloads.scripts;var Eb=Db.hasOwnProperty(Cb)?Db[Cb]:void 0;if(null!==Eb){Db[Cb]=null;var tc=c;if(Eb){2===Eb.length&&(tc=C({},c),lc(tc,Eb));var Rd=Qd.get(Cb);Rd&&(Rd.length=0)}var Sd=[];e.scripts.add(Sd);Kb(Sd,tc)}g&&a.push(Ob);Pd=null}return Pd;case "style":var Fb=c.precedence,Da=c.href;if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp||"string"!==typeof Fb||"string"!==typeof Da||""===Da){a.push(V("style"));var Sa=null,Td=null,lb;
for(lb in c)if(E.call(c,lb)){var Gb=c[lb];if(null!=Gb)switch(lb){case "children":Sa=Gb;break;case "dangerouslySetInnerHTML":Td=Gb;break;default:S(a,lb,Gb)}}a.push(U);var mb=Array.isArray(Sa)?2>Sa.length?Sa[0]:null:Sa;"function"!==typeof mb&&"symbol"!==typeof mb&&null!==mb&&void 0!==mb&&a.push(z(H(""+mb)));ec(a,Td,Sa);a.push(oc("style"));var Ud=null}else{var Ea=e.styles.get(Fb);if(null!==(d.styleResources.hasOwnProperty(Da)?d.styleResources[Da]:void 0)){d.styleResources[Da]=null;Ea?Ea.hrefs.push(z(H(Da))):
(Ea={precedence:z(H(Fb)),rules:[],hrefs:[z(H(Da))],sheets:new Map},e.styles.set(Fb,Ea));var Vd=Ea.rules,Ta=null,Wd=null,Hb;for(Hb in c)if(E.call(c,Hb)){var uc=c[Hb];if(null!=uc)switch(Hb){case "children":Ta=uc;break;case "dangerouslySetInnerHTML":Wd=uc}}var nb=Array.isArray(Ta)?2>Ta.length?Ta[0]:null:Ta;"function"!==typeof nb&&"symbol"!==typeof nb&&null!==nb&&void 0!==nb&&Vd.push(z(H(""+nb)));ec(Vd,Wd,Ta)}Ea&&e.boundaryResources&&e.boundaryResources.styles.add(Ea);g&&a.push(Ob);Ud=void 0}return Ud;
case "meta":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Xd=mc(a,c,"meta");else g&&a.push(Ob),Xd="string"===typeof c.charSet?mc(e.charsetChunks,c,"meta"):"viewport"===c.name?mc(e.preconnectChunks,c,"meta"):mc(e.hoistableChunks,c,"meta");return Xd;case "listing":case "pre":a.push(V(b));var ob=null,pb=null,qb;for(qb in c)if(E.call(c,qb)){var Ib=c[qb];if(null!=Ib)switch(qb){case "children":ob=Ib;break;case "dangerouslySetInnerHTML":pb=Ib;break;default:S(a,qb,Ib)}}a.push(U);if(null!=pb){if(null!=
ob)throw Error(l(60));if("object"!==typeof pb||!("__html"in pb))throw Error(l(61));var Fa=pb.__html;null!==Fa&&void 0!==Fa&&("string"===typeof Fa&&0<Fa.length&&"\n"===Fa[0]?a.push(zc,z(Fa)):a.push(z(""+Fa)))}"string"===typeof ob&&"\n"===ob[0]&&a.push(zc);return ob;case "img":var N=c.src,I=c.srcSet;if(!("lazy"===c.loading||!N&&!I||"string"!==typeof N&&null!=N||"string"!==typeof I&&null!=I)&&"low"!==c.fetchPriority&&!1===!!(f.tagScope&2)&&("string"!==typeof N||":"!==N[4]||"d"!==N[0]&&"D"!==N[0]||"a"!==
N[1]&&"A"!==N[1]||"t"!==N[2]&&"T"!==N[2]||"a"!==N[3]&&"A"!==N[3])&&("string"!==typeof I||":"!==I[4]||"d"!==I[0]&&"D"!==I[0]||"a"!==I[1]&&"A"!==I[1]||"t"!==I[2]&&"T"!==I[2]||"a"!==I[3]&&"A"!==I[3])){var Yd="string"===typeof c.sizes?c.sizes:void 0,Ua=I?I+"\n"+(Yd||""):N,vc=e.preloads.images,Ga=vc.get(Ua);if(Ga){if("high"===c.fetchPriority||10>e.highImagePreloads.size)vc.delete(Ua),e.highImagePreloads.add(Ga)}else if(!d.imageResources.hasOwnProperty(Ua)){d.imageResources[Ua]=J;var wc=c.crossOrigin;var Zd=
"string"===typeof wc?"use-credentials"===wc?wc:"":void 0;var ja=e.headers,xc;ja&&0<ja.remainingCapacity&&("high"===c.fetchPriority||500>ja.highImagePreloads.length)&&(xc=Ec(N,"image",{imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:Zd,integrity:c.integrity,nonce:c.nonce,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.refererPolicy}),2<=(ja.remainingCapacity-=xc.length))?(e.resets.image[Ua]=J,ja.highImagePreloads&&(ja.highImagePreloads+=", "),ja.highImagePreloads+=xc):(Ga=[],K(Ga,{rel:"preload",
as:"image",href:I?void 0:N,imageSrcSet:I,imageSizes:Yd,crossOrigin:Zd,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(Ga):(e.bulkPreloads.add(Ga),vc.set(Ua,Ga)))}}return mc(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return mc(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;
case "head":if(2>f.insertionMode&&null===e.headChunks){e.headChunks=[];var $d=pc(e.headChunks,c,"head")}else $d=pc(a,c,"head");return $d;case "html":if(0===f.insertionMode&&null===e.htmlChunks){e.htmlChunks=[Cc];var ae=pc(e.htmlChunks,c,"html")}else ae=pc(a,c,"html");return ae;default:if(-1!==b.indexOf("-")){a.push(V(b));var yc=null,be=null,Va;for(Va in c)if(E.call(c,Va)){var ca=c[Va];if(null!=ca){var ce=Va;switch(Va){case "children":yc=ca;break;case "dangerouslySetInnerHTML":be=ca;break;case "style":Ub(a,
ca);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;case "className":ce="class";default:if(ra(Va)&&"function"!==typeof ca&&"symbol"!==typeof ca&&!1!==ca){if(!0===ca)ca="";else if("object"===typeof ca)continue;a.push(Q,z(ce),Vb,z(H(ca)),P)}}}}a.push(U);ec(a,be,yc);return yc}}return pc(a,c,b)}var Fc=new Map;function oc(a){var b=Fc.get(a);void 0===b&&(b=B("</"+a+">"),Fc.set(a,b));return b}
function Gc(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)v(a,b[c]);return c<b.length?(c=b[c],b.length=0,w(a,c)):!0}var Hc=B('<template id="'),Ic=B('"></template>'),Jc=B("\x3c!--$--\x3e"),Kc=B('\x3c!--$?--\x3e<template id="'),Lc=B('"></template>'),Mc=B("\x3c!--$!--\x3e"),Nc=B("\x3c!--/$--\x3e"),Oc=B("<template"),Pc=B('"'),Qc=B(' data-dgst="');B(' data-msg="');B(' data-stck="');var Rc=B("></template>");
function Sc(a,b,c){v(a,Kc);if(null===c)throw Error(l(395));v(a,b.boundaryPrefix);v(a,z(c.toString(16)));return w(a,Lc)}
var Tc=B('<div hidden id="'),Uc=B('">'),Vc=B("</div>"),Wc=B('<svg aria-hidden="true" style="display:none" id="'),Xc=B('">'),Yc=B("</svg>"),Zc=B('<math aria-hidden="true" style="display:none" id="'),$c=B('">'),ad=B("</math>"),bd=B('<table hidden id="'),cd=B('">'),dd=B("</table>"),ed=B('<table hidden><tbody id="'),fd=B('">'),gd=B("</tbody></table>"),hd=B('<table hidden><tr id="'),id=B('">'),jd=B("</tr></table>"),kd=B('<table hidden><colgroup id="'),ld=B('">'),md=B("</colgroup></table>");
function nd(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return v(a,Tc),v(a,b.segmentPrefix),v(a,z(d.toString(16))),w(a,Uc);case 3:return v(a,Wc),v(a,b.segmentPrefix),v(a,z(d.toString(16))),w(a,Xc);case 4:return v(a,Zc),v(a,b.segmentPrefix),v(a,z(d.toString(16))),w(a,$c);case 5:return v(a,bd),v(a,b.segmentPrefix),v(a,z(d.toString(16))),w(a,cd);case 6:return v(a,ed),v(a,b.segmentPrefix),v(a,z(d.toString(16))),w(a,fd);case 7:return v(a,hd),v(a,b.segmentPrefix),v(a,z(d.toString(16))),w(a,id);
case 8:return v(a,kd),v(a,b.segmentPrefix),v(a,z(d.toString(16))),w(a,ld);default:throw Error(l(397));}}function od(a,b){switch(b.insertionMode){case 0:case 1:case 2:return w(a,Vc);case 3:return w(a,Yc);case 4:return w(a,ad);case 5:return w(a,dd);case 6:return w(a,gd);case 7:return w(a,jd);case 8:return w(a,md);default:throw Error(l(397));}}
var pd=B('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("'),qd=B('$RS("'),rd=B('","'),sd=B('")\x3c/script>'),td=B('<template data-rsi="" data-sid="'),ud=B('" data-pid="'),vd=B('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("'),
wd=B('$RC("'),xd=B('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
yd=B('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
zd=B('$RR("'),Ad=B('","'),Bd=B('",'),de=B('"'),ee=B(")\x3c/script>"),fe=B('<template data-rci="" data-bid="'),ge=B('<template data-rri="" data-bid="'),he=B('" data-sid="'),ie=B('" data-sty="'),je=B('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("'),ke=B('$RX("'),le=B('"'),me=B(","),ne=B(")\x3c/script>"),oe=B('<template data-rxi="" data-bid="'),pe=B('" data-dgst="'),
qe=B('" data-msg="'),re=B('" data-stck="'),se=/[<\u2028\u2029]/g;function te(a){return JSON.stringify(a).replace(se,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var ue=/[&><\u2028\u2029]/g;
function ve(a){return JSON.stringify(a).replace(ue,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}
var we=B('<style media="not all" data-precedence="'),xe=B('" data-href="'),ye=B('">'),ze=B("</style>"),Ae=!1,Be=!0;function Ce(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){v(this,we);v(this,a.precedence);for(v(this,xe);d<c.length-1;d++)v(this,c[d]),v(this,De);v(this,c[d]);v(this,ye);for(d=0;d<b.length;d++)v(this,b[d]);Be=w(this,ze);Ae=!0;b.length=0;c.length=0}}function Ee(a){return 2!==a.state?Ae=!0:!1}
function Fe(a,b,c){Ae=!1;Be=!0;b.styles.forEach(Ce,a);b.stylesheets.forEach(Ee);Ae&&(c.stylesToHoist=!0);return Be}function Ge(a){for(var b=0;b<a.length;b++)v(this,a[b]);a.length=0}var He=[];function Ie(a){K(He,a.props);for(var b=0;b<He.length;b++)v(this,He[b]);He.length=0;a.state=2}var Je=B('<style data-precedence="'),Ke=B('" data-href="'),De=B(" "),Le=B('">'),Me=B("</style>");
function Ne(a){var b=0<a.sheets.size;a.sheets.forEach(Ie,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){v(this,Je);v(this,a.precedence);a=0;if(d.length){for(v(this,Ke);a<d.length-1;a++)v(this,d[a]),v(this,De);v(this,d[a])}v(this,Le);for(a=0;a<c.length;a++)v(this,c[a]);v(this,Me);c.length=0;d.length=0}}
function Oe(a){if(0===a.state){a.state=1;var b=a.props;K(He,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<He.length;a++)v(this,He[a]);He.length=0}}function Pe(a){a.sheets.forEach(Oe,this);a.sheets.clear()}var Qe=B("["),Re=B(",["),Se=B(","),Te=B("]");
function Ue(a,b){v(a,Qe);var c=Qe;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)v(a,c),v(a,z(ve(""+d.props.href))),v(a,Te),c=Re;else{v(a,c);var e=d.props["data-precedence"],f=d.props;v(a,z(ve(""+d.props.href)));e=""+e;v(a,Se);v(a,z(ve(e)));for(var g in f)if(E.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error(l(399,"link"));default:a:{e=a;var k=g.toLowerCase();
switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!ra(g))break a;h=""+h}v(e,Se);v(e,z(ve(k)));v(e,Se);v(e,z(ve(h)))}}}v(a,Te);c=Re;d.state=3}});v(a,Te)}
function Ve(a,b){v(a,Qe);var c=Qe;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)v(a,c),v(a,z(H(JSON.stringify(""+d.props.href)))),v(a,Te),c=Re;else{v(a,c);var e=d.props["data-precedence"],f=d.props;v(a,z(H(JSON.stringify(""+d.props.href))));e=""+e;v(a,Se);v(a,z(H(JSON.stringify(e))));for(var g in f)if(E.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error(l(399,
"link"));default:a:{e=a;var k=g.toLowerCase();switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!ra(g))break a;h=""+h}v(e,Se);v(e,z(H(JSON.stringify(k))));
v(e,Se);v(e,z(H(JSON.stringify(h))))}}}v(a,Te);c=Re;d.state=3}});v(a,Te)}
function Na(a){var b=W?W:null;if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){c.dnsResources[a]=null;c=d.headers;var e,f;if(f=c&&0<c.remainingCapacity)f=(e="<"+(""+a).replace(We,Xe)+">; rel=dns-prefetch",2<=(c.remainingCapacity-=e.length));f?(d.resets.dns[a]=null,c.preconnects&&(c.preconnects+=", "),c.preconnects+=e):(e=[],K(e,{href:a,rel:"dns-prefetch"}),d.preconnects.add(e))}Ye(b)}}}
function Oa(a,b){var c=W?W:null;if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){var f="use-credentials"===b?"credentials":"string"===typeof b?"anonymous":"default";if(!d.connectResources[f].hasOwnProperty(a)){d.connectResources[f][a]=null;d=e.headers;var g,h;if(h=d&&0<d.remainingCapacity){h="<"+(""+a).replace(We,Xe)+">; rel=preconnect";if("string"===typeof b){var k=(""+b).replace(Ze,$e);h+='; crossorigin="'+k+'"'}h=(g=h,2<=(d.remainingCapacity-=g.length))}h?(e.resets.connect[f][a]=
null,d.preconnects&&(d.preconnects+=", "),d.preconnects+=g):(f=[],K(f,{rel:"preconnect",href:a,crossOrigin:b}),e.preconnects.add(f))}Ye(c)}}}
function Wa(a,b,c){var d=W?W:null;if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var k=c.fetchPriority}var m=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(m))return;e.imageResources[m]=J;e=f.headers;var p;e&&0<e.remainingCapacity&&"high"===k&&(p=Ec(a,b,c),2<=(e.remainingCapacity-=p.length))?(f.resets.image[m]=J,e.highImagePreloads&&(e.highImagePreloads+=", "),e.highImagePreloads+=p):(e=[],K(e,C({rel:"preload",href:g?
void 0:a,as:b},c)),"high"===k?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(m,e)));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];K(g,C({rel:"preload",href:a,as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?J:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);
K(g,C({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?J:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;g[a]=J;if((e=f.headers)&&0<e.remainingCapacity&&"font"===b&&(m=Ec(a,b,c),2<=(e.remainingCapacity-=m.length)))f.resets.font[a]=J,e.fontPreloads&&(e.fontPreloads+=", "),e.fontPreloads+=m;else switch(e=[],
a=C({rel:"preload",href:a,as:b},c),K(e,a),b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}}Ye(d)}}}
function Xa(a,b){var c=W?W:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?J:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=J}K(f,C({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);Ye(c)}}}
function Ya(a,b,c){var d=W?W:null;if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:z(H(b)),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:C({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&lc(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),Ye(d))}}}
function Za(a,b){var c=W?W:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=C({src:a,async:!0},b),f&&(2===f.length&&lc(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Kb(a,b),Ye(c))}}}
function $a(a,b){var c=W?W:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=C({src:a,type:"module",async:!0},b),f&&(2===f.length&&lc(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Kb(a,b),Ye(c))}}}function lc(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}
function Ec(a,b,c){a=(""+a).replace(We,Xe);b=(""+b).replace(Ze,$e);b="<"+a+'>; rel=preload; as="'+b+'"';for(var d in c)E.call(c,d)&&(a=c[d],"string"===typeof a&&(b+="; "+d.toLowerCase()+'="'+(""+a).replace(Ze,$e)+'"'));return b}var We=/[<>\r\n]/g;
function Xe(a){switch(a){case "<":return"%3C";case ">":return"%3E";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeLinkHrefForHeaderContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}var Ze=/["';,\r\n]/g;
function $e(a){switch(a){case '"':return"%22";case "'":return"%27";case ";":return"%3B";case ",":return"%2C";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeStringForLinkHeaderQuotedParamValueContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}function af(a){this.styles.add(a)}function bf(a){this.stylesheets.add(a)}
var cf=Symbol.for("react.element"),df=Symbol.for("react.portal"),ef=Symbol.for("react.fragment"),ff=Symbol.for("react.strict_mode"),gf=Symbol.for("react.profiler"),hf=Symbol.for("react.provider"),jf=Symbol.for("react.context"),kf=Symbol.for("react.server_context"),lf=Symbol.for("react.forward_ref"),mf=Symbol.for("react.suspense"),nf=Symbol.for("react.suspense_list"),of=Symbol.for("react.memo"),pf=Symbol.for("react.lazy"),qf=Symbol.for("react.scope"),rf=Symbol.for("react.debug_trace_mode"),sf=Symbol.for("react.offscreen"),
tf=Symbol.for("react.legacy_hidden"),uf=Symbol.for("react.cache"),vf=Symbol.for("react.default_value"),xf=Symbol.for("react.memo_cache_sentinel"),yf=Symbol.for("react.postpone"),zf=Symbol.iterator;
function Af(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case ef:return"Fragment";case df:return"Portal";case gf:return"Profiler";case ff:return"StrictMode";case mf:return"Suspense";case nf:return"SuspenseList";case uf:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case jf:return(a.displayName||"Context")+".Consumer";case hf:return(a._context.displayName||"Context")+".Provider";case lf:var b=a.render;a=a.displayName;
a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case of:return b=a.displayName||null,null!==b?b:Af(a.type)||"Memo";case pf:b=a._payload;a=a._init;try{return Af(a(b))}catch(c){break}case kf:return(a.displayName||a._globalName)+".Provider"}return null}var Bf={};function Cf(a,b){a=a.contextTypes;if(!a)return Bf;var c={},d;for(d in a)c[d]=b[d];return c}var Df=null;
function Ef(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error(l(401));}else{if(null===c)throw Error(l(401));Ef(a,c)}b.context._currentValue=b.value}}function Ff(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Ff(a)}function Gf(a){var b=a.parent;null!==b&&Gf(b);a.context._currentValue=a.value}
function Hf(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error(l(402));a.depth===b.depth?Ef(a,b):Hf(a,b)}function If(a,b){var c=b.parent;if(null===c)throw Error(l(402));a.depth===c.depth?Ef(a,c):If(a,c);b.context._currentValue=b.value}function Jf(a){var b=Df;b!==a&&(null===b?Gf(a):null===a?Ff(b):b.depth===a.depth?Ef(b,a):b.depth>a.depth?Hf(b,a):If(b,a),Df=a)}
var Kf={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function Lf(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=Kf;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:C({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&Kf.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=C({},f,h)):C(f,h))}a.state=f}else f.queue=null}
var Mf={id:1,overflow:""};function Nf(a,b,c){var d=a.id;a=a.overflow;var e=32-Of(d)-1;d&=~(1<<e);c+=1;var f=32-Of(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-Of(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var Of=Math.clz32?Math.clz32:Pf,Qf=Math.log,Rf=Math.LN2;function Pf(a){a>>>=0;return 0===a?32:31-(Qf(a)/Rf|0)|0}var Sf=Error(l(460));function Tf(){}
function Uf(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Tf,Tf),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Vf=b;throw Sf;}}var Vf=null;
function Wf(){if(null===Vf)throw Error(l(459));var a=Vf;Vf=null;return a}function Xf(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var Yf="function"===typeof Object.is?Object.is:Xf,Zf=null,$f=null,ag=null,bg=null,cg=null,Y=null,dg=!1,eg=!1,fg=0,gg=0,hg=-1,ig=0,jg=null,kg=null,lg=0;function mg(){if(null===Zf)throw Error(l(321));return Zf}function ng(){if(0<lg)throw Error(l(312));return{memoizedState:null,queue:null,next:null}}
function og(){null===Y?null===cg?(dg=!1,cg=Y=ng()):(dg=!0,Y=cg):null===Y.next?(dg=!1,Y=Y.next=ng()):(dg=!0,Y=Y.next);return Y}function pg(a,b,c,d){for(;eg;)eg=!1,gg=fg=0,hg=-1,ig=0,lg+=1,Y=null,c=a(b,d);qg();return c}function rg(){var a=jg;jg=null;return a}function qg(){bg=ag=$f=Zf=null;eg=!1;cg=null;lg=0;Y=kg=null}function sg(a,b){return"function"===typeof b?b(a):b}
function tg(a,b,c){Zf=mg();Y=og();if(dg){var d=Y.queue;b=d.dispatch;if(null!==kg&&(c=kg.get(d),void 0!==c)){kg.delete(d);d=Y.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);Y.memoizedState=d;return[d,b]}return[Y.memoizedState,b]}a=a===sg?"function"===typeof b?b():b:void 0!==c?c(b):b;Y.memoizedState=a;a=Y.queue={last:null,dispatch:null};a=a.dispatch=ug.bind(null,Zf,a);return[Y.memoizedState,a]}
function vg(a,b){Zf=mg();Y=og();b=void 0===b?null:b;if(null!==Y){var c=Y.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!Yf(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();Y.memoizedState=[a,b];return a}function ug(a,b,c){if(25<=lg)throw Error(l(301));if(a===Zf)if(eg=!0,a={action:c,next:null},null===kg&&(kg=new Map),c=kg.get(b),void 0===c)kg.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}
function wg(){throw Error(l(440));}function xg(){throw Error(l(394));}function yg(){throw Error(l(479));}function zg(a){var b=ig;ig+=1;null===jg&&(jg=[]);return Uf(jg,a,b)}function Ag(){throw Error(l(393));}function Bg(){}
var Dg={readContext:function(a){return a._currentValue},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return zg(a);if(a.$$typeof===jf||a.$$typeof===kf)return a._currentValue}throw Error(l(438,String(a)));},useContext:function(a){mg();return a._currentValue},useMemo:vg,useReducer:tg,useRef:function(a){Zf=mg();Y=og();var b=Y.memoizedState;return null===b?(a={current:a},Y.memoizedState=a):b},useState:function(a){return tg(sg,a)},useInsertionEffect:Bg,useLayoutEffect:Bg,
useCallback:function(a,b){return vg(function(){return a},b)},useImperativeHandle:Bg,useEffect:Bg,useDebugValue:Bg,useDeferredValue:function(a,b){mg();return void 0!==b?b:a},useTransition:function(){mg();return[!1,xg]},useId:function(){var a=$f.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-Of(a)-1)).toString(32)+b;var c=Cg;if(null===c)throw Error(l(404));b=fg++;a=":"+c.idPrefix+"R"+a;0<b&&(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error(l(407));
return c()},useCacheRefresh:function(){return Ag},useEffectEvent:function(){return wg},useMemoCache:function(a){for(var b=Array(a),c=0;c<a;c++)b[c]=xf;return b},useHostTransitionStatus:function(){mg();return La},useOptimistic:function(a){mg();return[a,yg]},useFormState:function(a,b,c){mg();var d=gg++,e=ag;if("function"===typeof a.$$FORM_ACTION){var f=null,g=bg;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;if(null!==e&&"function"===typeof h){var k=e[1];h.call(a,e[2],e[3])&&(f=void 0!==c?"p"+c:"k"+ea(JSON.stringify([g,
null,d]),0),k===f&&(hg=d,b=e[0]))}var m=a.bind(null,b);a=function(t){m(t)};"function"===typeof m.$$FORM_ACTION&&(a.$$FORM_ACTION=function(t){t=m.$$FORM_ACTION(t);void 0!==c&&(c+="",t.action=c);var q=t.data;q&&(null===f&&(f=void 0!==c?"p"+c:"k"+ea(JSON.stringify([g,null,d]),0)),q.append("$ACTION_KEY",f));return t});return[b,a]}var p=a.bind(null,b);return[b,function(t){p(t)}]}},Cg=null,Eg={getCacheSignal:function(){throw Error(l(248));},getCacheForType:function(){throw Error(l(248));}},Fg=Ka.ReactCurrentDispatcher,
Gg=Ka.ReactCurrentCache;function Hg(a){console.error(a);return null}function Ig(){}
function Jg(a,b,c,d,e,f,g,h,k,m,p,t){Ma.current=ab;var q=[],x=new Set;b={destination:null,flushScheduled:!1,resumableState:b,renderState:c,rootFormatContext:d,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:x,pingedTasks:q,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===f?Hg:f,onPostpone:void 0===p?Ig:p,onAllReady:void 0===g?
Ig:g,onShellReady:void 0===h?Ig:h,onShellError:void 0===k?Ig:k,onFatalError:void 0===m?Ig:m,formState:void 0===t?null:t};c=Kg(b,0,null,d,!1,!1);c.parentFlushed=!0;a=Lg(b,null,a,-1,null,c,x,null,d,Bf,null,Mf);q.push(a);return b}function Mg(a,b,c,d,e,f,g,h,k,m,p){a=Jg(a,b,c,d,e,f,g,h,k,m,p,void 0);a.trackedPostpones={workingMap:new Map,rootNodes:[],rootSlots:null};return a}
function Ng(a,b,c,d,e,f,g,h,k){Ma.current=ab;var m=[],p=new Set;c={destination:null,flushScheduled:!1,resumableState:b.resumableState,renderState:c,rootFormatContext:b.rootFormatContext,progressiveChunkSize:b.progressiveChunkSize,status:0,fatalError:null,nextSegmentId:b.nextSegmentId,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:p,pingedTasks:m,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===d?Hg:d,onPostpone:void 0===
k?Ig:k,onAllReady:void 0===e?Ig:e,onShellReady:void 0===f?Ig:f,onShellError:void 0===g?Ig:g,onFatalError:void 0===h?Ig:h,formState:null};if("number"===typeof b.replaySlots)return d=b.replaySlots,e=Kg(c,0,null,b.rootFormatContext,!1,!1),e.id=d,e.parentFlushed=!0,a=Lg(c,null,a,-1,null,e,p,null,b.rootFormatContext,Bf,null,Mf),m.push(a),c;a=Og(c,null,{nodes:b.replayNodes,slots:b.replaySlots,pendingTasks:0},a,-1,null,p,null,b.rootFormatContext,Bf,null,Mf);m.push(a);return c}var W=null;
function Pg(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,Qg(a))}function Rg(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,resources:{styles:new Set,stylesheets:new Set},trackedContentKeyPath:null,trackedFallbackNode:null}}
function Lg(a,b,c,d,e,f,g,h,k,m,p,t){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var q={replay:null,node:c,childIndex:d,ping:function(){return Pg(a,q)},blockedBoundary:e,blockedSegment:f,abortSet:g,keyPath:h,formatContext:k,legacyContext:m,context:p,treeContext:t,thenableState:b};g.add(q);return q}
function Og(a,b,c,d,e,f,g,h,k,m,p,t){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var q={replay:c,node:d,childIndex:e,ping:function(){return Pg(a,q)},blockedBoundary:f,blockedSegment:null,abortSet:g,keyPath:h,formatContext:k,legacyContext:m,context:p,treeContext:t,thenableState:b};g.add(q);return q}function Kg(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}
function Sg(a,b){a=a.onError(b);if(null!=a&&"string"!==typeof a)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof a+'" instead');return a}function Tg(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,ka(a.destination,b)):(a.status=1,a.fatalError=b)}
function Ug(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error(l(108,Af(e)||"Unknown",h));e=C({},c,d)}b.legacyContext=e;Z(a,b,null,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,Z(a,b,null,f,-1),b.keyPath=e}
function Vg(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var k=b.blockedSegment;if(null!==k){h=!0;k=k.chunks;for(var m=0;m<f;m++)m===g?k.push(ic):k.push(jc)}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=Nf(c,1,0),Wg(a,b,d,-1),b.treeContext=c):h?Wg(a,b,d,-1):Z(a,b,null,d,-1);b.keyPath=f}function Xg(a,b){if(a&&a.defaultProps){b=C({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function Yg(a,b,c,d,e,f,g){if("function"===typeof e)if(e.prototype&&e.prototype.isReactComponent){d=Cf(e,b.legacyContext);var h=e.contextType;h=new e(f,"object"===typeof h&&null!==h?h._currentValue:d);Lf(h,e,f,d);Ug(a,b,c,h,e)}else{h=Cf(e,b.legacyContext);Zf={};$f=b;ag=a;bg=c;gg=fg=0;hg=-1;ig=0;jg=d;d=e(f,h);d=pg(e,f,d,h);g=0!==fg;var k=gg,m=hg;"object"===typeof d&&null!==d&&"function"===typeof d.render&&void 0===d.$$typeof?(Lf(d,e,f,h),Ug(a,b,c,d,e)):Vg(a,b,c,d,g,k,m)}else if("string"===typeof e)if(d=
b.blockedSegment,null===d)d=f.children,h=b.formatContext,g=b.keyPath,b.formatContext=Nb(h,e,f),b.keyPath=c,Wg(a,b,d,-1),b.formatContext=h,b.keyPath=g;else{g=Dc(d.chunks,e,f,a.resumableState,a.renderState,b.formatContext,d.lastPushedText);d.lastPushedText=!1;h=b.formatContext;k=b.keyPath;b.formatContext=Nb(h,e,f);b.keyPath=c;Wg(a,b,g,-1);b.formatContext=h;b.keyPath=k;a:{b=d.chunks;a=a.resumableState;switch(e){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;
case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}b.push(oc(e))}d.lastPushedText=!1}else{switch(e){case tf:case rf:case ff:case gf:case ef:e=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=e;return;case sf:"hidden"!==f.mode&&(e=b.keyPath,b.keyPath=c,Z(a,b,null,f.children,-1),b.keyPath=e);return;case nf:e=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=e;return;case qf:throw Error(l(343));case mf:a:if(null!==
b.replay){e=b.keyPath;b.keyPath=c;c=f.children;try{Wg(a,b,c,-1)}finally{b.keyPath=e}}else{m=b.keyPath;e=b.blockedBoundary;var p=b.blockedSegment;d=f.fallback;var t=f.children;f=new Set;g=Rg(a,f);null!==a.trackedPostpones&&(g.trackedContentKeyPath=c);k=Kg(a,p.chunks.length,g,b.formatContext,!1,!1);p.children.push(k);p.lastPushedText=!1;var q=Kg(a,0,null,b.formatContext,!1,!1);q.parentFlushed=!0;b.blockedBoundary=g;b.blockedSegment=q;a.renderState.boundaryResources=g.resources;b.keyPath=c;try{if(Wg(a,
b,t,-1),q.lastPushedText&&q.textEmbedded&&q.chunks.push(Ob),q.status=1,Zg(g,q),0===g.pendingTasks&&0===g.status){g.status=1;break a}}catch(x){q.status=4,g.status=4,"object"===typeof x&&null!==x&&x.$$typeof===yf?(a.onPostpone(x.message),h="POSTPONE"):h=Sg(a,x),g.errorDigest=h}finally{a.renderState.boundaryResources=e?e.resources:null,b.blockedBoundary=e,b.blockedSegment=p,b.keyPath=m}h=[c[0],"Suspense Fallback",c[2]];m=a.trackedPostpones;null!==m&&(p=[h[1],h[2],[],null],m.workingMap.set(h,p),5===g.status?
m.workingMap.get(c)[4]=p:g.trackedFallbackNode=p);b=Lg(a,null,d,-1,e,k,f,h,b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}return}if("object"===typeof e&&null!==e)switch(e.$$typeof){case lf:e=e.render;Zf={};$f=b;ag=a;bg=c;gg=fg=0;hg=-1;ig=0;jg=d;d=e(f,g);f=pg(e,f,d,g);Vg(a,b,c,f,0!==fg,gg,hg);return;case of:e=e.type;f=Xg(e,f);Yg(a,b,c,d,e,f,g);return;case hf:h=f.children;d=b.keyPath;e=e._context;f=f.value;g=e._currentValue;e._currentValue=f;k=Df;Df=f={parent:k,depth:null===
k?0:k.depth+1,context:e,parentValue:g,value:f};b.context=f;b.keyPath=c;Z(a,b,null,h,-1);a=Df;if(null===a)throw Error(l(403));c=a.parentValue;a.context._currentValue=c===vf?a.context._defaultValue:c;a=Df=a.parent;b.context=a;b.keyPath=d;return;case jf:f=f.children;f=f(e._currentValue);e=b.keyPath;b.keyPath=c;Z(a,b,null,f,-1);b.keyPath=e;return;case pf:h=e._init;e=h(e._payload);f=Xg(e,f);Yg(a,b,c,d,e,f,void 0);return}throw Error(l(130,null==e?e:typeof e,""));}}
function $g(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=Kg(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,Wg(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(Zg(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function Z(a,b,c,d,e){if(null!==b.replay&&"number"===typeof b.replay.slots)$g(a,b,b.replay.slots,d,e);else{b.node=d;b.childIndex=e;if("object"===typeof d&&null!==d){switch(d.$$typeof){case cf:var f=d.type,g=d.key,h=d.props,k=d.ref,m=Af(f),p=null==g?-1===e?0:e:g;g=[b.keyPath,m,p];if(null!==b.replay)a:{var t=b.replay;e=t.nodes;for(d=0;d<e.length;d++){var q=e[d];if(p===q[1]){if(4===q.length){if(null!==m&&m!==q[0])throw Error(l(490,q[0],m));m=q[2];q=q[3];p=b.node;b.replay={nodes:m,slots:q,pendingTasks:1};
try{Yg(a,b,g,c,f,h,k);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error(l(488));b.replay.pendingTasks--}catch(F){if("object"===typeof F&&null!==F&&(F===Sf||"function"===typeof F.then))throw b.node===p&&(b.replay=t),F;b.replay.pendingTasks--;ah(a,b.blockedBoundary,F,m,q)}b.replay=t}else{if(f!==mf)throw Error(l(490,"Suspense",Af(f)||"Unknown"));b:{c=void 0;f=q[5];k=q[2];t=q[3];m=null===q[4]?[]:q[4][2];q=null===q[4]?null:q[4][3];p=b.keyPath;var x=b.replay,A=b.blockedBoundary,X=h.children;
h=h.fallback;var u=new Set,D=Rg(a,u);D.parentFlushed=!0;D.rootSegmentID=f;b.blockedBoundary=D;b.replay={nodes:k,slots:t,pendingTasks:1};a.renderState.boundaryResources=D.resources;try{Wg(a,b,X,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error(l(488));b.replay.pendingTasks--;if(0===D.pendingTasks&&0===D.status){D.status=1;a.completedBoundaries.push(D);break b}}catch(F){D.status=4,"object"===typeof F&&null!==F&&F.$$typeof===yf?(a.onPostpone(F.message),c="POSTPONE"):c=Sg(a,F),D.errorDigest=
c,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(D)}finally{a.renderState.boundaryResources=A?A.resources:null,b.blockedBoundary=A,b.replay=x,b.keyPath=p}h=Og(a,null,{nodes:m,slots:q,pendingTasks:0},h,-1,A,u,[g[0],"Suspense Fallback",g[2]],b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(h)}}e.splice(d,1);break a}}}else Yg(a,b,g,c,f,h,k);return;case df:throw Error(l(257));case pf:h=d._init;d=h(d._payload);Z(a,b,null,d,e);return}if(Ja(d)){bh(a,b,d,e);return}null===
d||"object"!==typeof d?h=null:(h=zf&&d[zf]||d["@@iterator"],h="function"===typeof h?h:null);if(h&&(h=h.call(d))){d=h.next();if(!d.done){g=[];do g.push(d.value),d=h.next();while(!d.done);bh(a,b,g,e)}return}if("function"===typeof d.then)return Z(a,b,null,zg(d),e);if(d.$$typeof===jf||d.$$typeof===kf)return Z(a,b,null,d._currentValue,e);e=Object.prototype.toString.call(d);throw Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(d).join(", ")+"}":e));}"string"===typeof d?(e=b.blockedSegment,
null!==e&&(e.lastPushedText=Pb(e.chunks,d,a.renderState,e.lastPushedText))):"number"===typeof d&&(e=b.blockedSegment,null!==e&&(e.lastPushedText=Pb(e.chunks,""+d,a.renderState,e.lastPushedText)))}}
function bh(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var k=g[h];if(k[1]===d){d=k[2];k=k[3];b.replay={nodes:d,slots:k,pendingTasks:1};try{bh(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error(l(488));b.replay.pendingTasks--}catch(p){if("object"===typeof p&&null!==p&&(p===Sf||"function"===typeof p.then))throw p;b.replay.pendingTasks--;ah(a,b.blockedBoundary,p,d,k)}b.replay=f;g.splice(h,
1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(k=0;k<g;k++){d=c[k];b.treeContext=Nf(f,g,k);var m=h[k];"number"===typeof m?($g(a,b,m,d,k),delete h[k]):Wg(a,b,d,k)}b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)k=c[h],b.treeContext=Nf(f,g,h),Wg(a,b,k,h);b.treeContext=f;b.keyPath=e}
function ch(a,b,c,d){d.status=5;var e=c.keyPath,f=c.blockedBoundary;if(null===f)d.id=a.nextSegmentId++,b.rootSlots=d.id,null!==a.completedRootSegment&&(a.completedRootSegment.status=5);else{if(null!==f&&0===f.status){f.status=5;f.rootSegmentID=a.nextSegmentId++;var g=f.trackedContentKeyPath;if(null===g)throw Error(l(486));var h=f.trackedFallbackNode,k=[];if(g===e&&-1===c.childIndex){-1===d.id&&(d.id=d.parentFlushed?f.rootSegmentID:a.nextSegmentId++);d=[g[1],g[2],k,d.id,h,f.rootSegmentID];b.workingMap.set(g,
d);dh(d,g[0],b);return}var m=b.workingMap.get(g);void 0===m?(m=[g[1],g[2],k,null,h,f.rootSegmentID],b.workingMap.set(g,m),dh(m,g[0],b)):(g=m,g[4]=h,g[5]=f.rootSegmentID)}-1===d.id&&(d.id=d.parentFlushed&&null!==f?f.rootSegmentID:a.nextSegmentId++);if(-1===c.childIndex)null===e?b.rootSlots=d.id:(c=b.workingMap.get(e),void 0===c?(c=[e[1],e[2],[],d.id],dh(c,e[0],b)):c[3]=d.id);else{if(null===e)if(a=b.rootSlots,null===a)a=b.rootSlots={};else{if("number"===typeof a)throw Error(l(491));}else if(f=b.workingMap,
g=f.get(e),void 0===g)a={},g=[e[1],e[2],[],a],f.set(e,g),dh(g,e[0],b);else if(a=g[3],null===a)a=g[3]={};else if("number"===typeof a)throw Error(l(491));a[c.childIndex]=d.id}}}
function Wg(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,k=b.treeContext,m=b.blockedSegment;if(null===m)try{return Z(a,b,null,c,d)}catch(q){if(qg(),d=q===Sf?Wf():q,"object"===typeof d&&null!==d&&"function"===typeof d.then){c=d;d=rg();a=Og(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;Jf(g);return}}else{var p=
m.children.length,t=m.chunks.length;try{return Z(a,b,null,c,d)}catch(q){if(qg(),m.children.length=p,m.chunks.length=t,d=q===Sf?Wf():q,"object"===typeof d&&null!==d){if("function"===typeof d.then){c=d;d=rg();m=b.blockedSegment;p=Kg(a,m.chunks.length,null,b.formatContext,m.lastPushedText,!0);m.children.push(p);m.lastPushedText=!1;a=Lg(a,d,b.node,b.childIndex,b.blockedBoundary,p,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=
f;b.context=g;b.keyPath=h;b.treeContext=k;Jf(g);return}if(null!==a.trackedPostpones&&d.$$typeof===yf&&null!==b.blockedBoundary){c=a.trackedPostpones;a.onPostpone(d.message);d=b.blockedSegment;m=Kg(a,d.chunks.length,null,b.formatContext,d.lastPushedText,!0);d.children.push(m);d.lastPushedText=!1;ch(a,c,b,m);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;Jf(g);return}}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;Jf(g);throw d;}
function ah(a,b,c,d,e){if("object"===typeof c&&null!==c&&c.$$typeof===yf){a.onPostpone(c.message);var f="POSTPONE"}else f=Sg(a,c);eh(a,b,d,e,c,f)}function fh(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,gh(this,b,a))}
function eh(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)eh(a,b,h[2],h[3],e,f);else{h=h[5];var k=a,m=f,p=Rg(k,new Set);p.parentFlushed=!0;p.rootSegmentID=h;p.status=4;p.errorDigest=m;p.parentFlushed&&k.clientRenderedBoundaries.push(p)}}c.length=0;if(null!==d){if(null===b)throw Error(l(487));4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var t in d)delete d[t]}}
function hh(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(1!==b.status&&2!==b.status){a=a.replay;if(null===a){Sg(b,c);Tg(b,c);return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&(d=Sg(b,c),eh(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&ih(b)}}else d.pendingTasks--,4!==d.status&&(d.status=4,d.errorDigest=Sg(b,c),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return hh(f,
b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&jh(b)}
function kh(a,b){try{var c=a.renderState,d=c.onHeaders;if(d){var e=c.headers;if(e){c.headers=null;var f=e.preconnects;e.fontPreloads&&(f&&(f+=", "),f+=e.fontPreloads);e.highImagePreloads&&(f&&(f+=", "),f+=e.highImagePreloads);if(!b){var g=c.styles.values(),h=g.next();b:for(;0<e.remainingCapacity&&!h.done;h=g.next())for(var k=h.value.sheets.values(),m=k.next();0<e.remainingCapacity&&!m.done;m=k.next()){var p=m.value,t=p.props,q=t.href,x=p.props,A=Ec(x.href,"style",{crossOrigin:x.crossOrigin,integrity:x.integrity,
nonce:x.nonce,type:x.type,fetchPriority:x.fetchPriority,referrerPolicy:x.referrerPolicy,media:x.media});if(2<=(e.remainingCapacity-=A.length))c.resets.style[q]=J,f&&(f+=", "),f+=A,c.resets.style[q]="string"===typeof t.crossOrigin||"string"===typeof t.integrity?[t.crossOrigin,t.integrity]:J;else break b}}f?d({Link:f}):d({})}}}catch(X){Sg(a,X)}}function ih(a){null===a.trackedPostpones&&kh(a,!0);a.onShellError=Ig;a=a.onShellReady;a()}
function jh(a){kh(a,null===a.trackedPostpones?!0:null===a.completedRootSegment||5!==a.completedRootSegment.status);a=a.onAllReady;a()}function Zg(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary&&-1===b.children[0].id){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&Zg(a,c)}else a.completedSegments.push(b)}
function gh(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error(l(389));a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&ih(a)}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&Zg(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(fh,a),b.fallbackAbortableTasks.clear())):null!==c&&c.parentFlushed&&1===c.status&&
(Zg(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&jh(a)}
function Qg(a){if(2!==a.status){var b=Df,c=Fg.current;Fg.current=Dg;var d=Gg.current;Gg.current=Eg;var e=W;W=a;var f=Cg;Cg=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var k=g[h],m=a,p=k.blockedBoundary;m.renderState.boundaryResources=p?p.resources:null;var t=k.blockedSegment;if(null===t){var q=m;if(0!==k.replay.pendingTasks){Jf(k.context);try{var x=k.thenableState;k.thenableState=null;Z(q,k,x,k.node,k.childIndex);if(1===k.replay.pendingTasks&&0<k.replay.nodes.length)throw Error(l(488));
k.replay.pendingTasks--;k.abortSet.delete(k);gh(q,k.blockedBoundary,null)}catch(M){qg();var A=M===Sf?Wf():M;if("object"===typeof A&&null!==A&&"function"===typeof A.then){var X=k.ping;A.then(X,X);k.thenableState=rg()}else k.replay.pendingTasks--,k.abortSet.delete(k),ah(q,k.blockedBoundary,A,k.replay.nodes,k.replay.slots),q.pendingRootTasks--,0===q.pendingRootTasks&&ih(q),q.allPendingTasks--,0===q.allPendingTasks&&jh(q)}finally{q.renderState.boundaryResources=null}}}else a:{q=void 0;var u=t;if(0===
u.status){Jf(k.context);var D=u.children.length,F=u.chunks.length;try{var ma=k.thenableState;k.thenableState=null;Z(m,k,ma,k.node,k.childIndex);u.lastPushedText&&u.textEmbedded&&u.chunks.push(Ob);k.abortSet.delete(k);u.status=1;gh(m,k.blockedBoundary,u)}catch(M){qg();u.children.length=D;u.chunks.length=F;var y=M===Sf?Wf():M;if("object"===typeof y&&null!==y){if("function"===typeof y.then){var T=k.ping;y.then(T,T);k.thenableState=rg();break a}if(null!==m.trackedPostpones&&y.$$typeof===yf){var G=m.trackedPostpones;
k.abortSet.delete(k);m.onPostpone(y.message);ch(m,G,k,u);gh(m,k.blockedBoundary,u);break a}}k.abortSet.delete(k);u.status=4;var L=k.blockedBoundary;"object"===typeof y&&null!==y&&y.$$typeof===yf?(m.onPostpone(y.message),q="POSTPONE"):q=Sg(m,y);null===L?Tg(m,y):(L.pendingTasks--,4!==L.status&&(L.status=4,L.errorDigest=q,L.parentFlushed&&m.clientRenderedBoundaries.push(L)));m.allPendingTasks--;0===m.allPendingTasks&&jh(m)}finally{m.renderState.boundaryResources=null}}}}g.splice(0,h);null!==a.destination&&
lh(a,a.destination)}catch(M){Sg(a,M),Tg(a,M)}finally{Cg=f,Fg.current=c,Gg.current=d,c===Dg&&Jf(b),W=e}}}
function mh(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:var d=c.id;c.lastPushedText=!1;c.textEmbedded=!1;a=a.renderState;v(b,Hc);v(b,a.placeholderPrefix);a=z(d.toString(16));v(b,a);return w(b,Ic);case 1:c.status=2;var e=!0;d=c.chunks;var f=0;c=c.children;for(var g=0;g<c.length;g++){for(e=c[g];f<e.index;f++)v(b,d[f]);e=nh(a,b,e)}for(;f<d.length-1;f++)v(b,d[f]);f<d.length&&(e=w(b,d[f]));return e;default:throw Error(l(390));}}
function nh(a,b,c){var d=c.boundary;if(null===d)return mh(a,b,c);d.parentFlushed=!0;if(4===d.status)d=d.errorDigest,w(b,Mc),v(b,Oc),d&&(v(b,Qc),v(b,z(H(d))),v(b,Pc)),w(b,Rc),mh(a,b,c);else if(1!==d.status)0===d.status&&(d.rootSegmentID=a.nextSegmentId++),0<d.completedSegments.length&&a.partialBoundaries.push(d),Sc(b,a.renderState,d.rootSegmentID),mh(a,b,c);else if(d.byteSize>a.progressiveChunkSize)d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),Sc(b,a.renderState,d.rootSegmentID),
mh(a,b,c);else{c=d.resources;var e=a.renderState.boundaryResources;e&&(c.styles.forEach(af,e),c.stylesheets.forEach(bf,e));w(b,Jc);d=d.completedSegments;if(1!==d.length)throw Error(l(391));nh(a,b,d[0])}return w(b,Nc)}function oh(a,b,c){nd(b,a.renderState,c.parentFormatContext,c.id);nh(a,b,c);return od(b,c.parentFormatContext)}
function ph(a,b,c){a.renderState.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)qh(a,b,c,d[e]);d.length=0;Fe(b,c.resources,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.resources;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(v(b,a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,v(b,512<xd.byteLength?xd.slice():xd)):0===(d.instructions&8)?(d.instructions|=8,v(b,yd)):v(b,zd):0===(d.instructions&2)?(d.instructions|=
2,v(b,vd)):v(b,wd)):f?v(b,ge):v(b,fe);d=z(e.toString(16));v(b,a.boundaryPrefix);v(b,d);g?v(b,Ad):v(b,he);v(b,a.segmentPrefix);v(b,d);f?g?(v(b,Bd),Ue(b,c)):(v(b,ie),Ve(b,c)):g&&v(b,de);d=g?w(b,ee):w(b,bb);return Gc(b,a)&&d}
function qh(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error(l(392));return oh(a,b,d)}if(e===c.rootSegmentID)return oh(a,b,d);oh(a,b,d);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(v(b,a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,v(b,pd)):v(b,qd)):v(b,td);v(b,a.segmentPrefix);e=z(e.toString(16));v(b,e);d?v(b,rd):v(b,ud);v(b,a.placeholderPrefix);v(b,e);b=d?w(b,sd):w(b,bb);return b}
function lh(a,b){n=new Uint8Array(512);r=0;try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,k=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(k))}var m=e.htmlChunks,p=e.headChunks;f=0;if(m){for(f=0;f<m.length;f++)v(b,m[f]);if(p)for(f=0;f<p.length;f++)v(b,p[f]);else v(b,
V("head")),v(b,U)}else if(p)for(f=0;f<p.length;f++)v(b,p[f]);var t=e.charsetChunks;for(f=0;f<t.length;f++)v(b,t[f]);t.length=0;e.preconnects.forEach(Ge,b);e.preconnects.clear();var q=e.preconnectChunks;for(f=0;f<q.length;f++)v(b,q[f]);q.length=0;e.fontPreloads.forEach(Ge,b);e.fontPreloads.clear();e.highImagePreloads.forEach(Ge,b);e.highImagePreloads.clear();e.styles.forEach(Ne,b);var x=e.importMapChunks;for(f=0;f<x.length;f++)v(b,x[f]);x.length=0;e.bootstrapScripts.forEach(Ge,b);e.scripts.forEach(Ge,
b);e.scripts.clear();e.bulkPreloads.forEach(Ge,b);e.bulkPreloads.clear();var A=e.preloadChunks;for(f=0;f<A.length;f++)v(b,A[f]);A.length=0;var X=e.hoistableChunks;for(f=0;f<X.length;f++)v(b,X[f]);X.length=0;m&&null===p&&v(b,oc("head"));nh(a,b,d);a.completedRootSegment=null;Gc(b,a.renderState)}else return;var u=a.renderState;d=0;u.preconnects.forEach(Ge,b);u.preconnects.clear();var D=u.preconnectChunks;for(d=0;d<D.length;d++)v(b,D[d]);D.length=0;u.fontPreloads.forEach(Ge,b);u.fontPreloads.clear();
u.highImagePreloads.forEach(Ge,b);u.highImagePreloads.clear();u.styles.forEach(Pe,b);u.scripts.forEach(Ge,b);u.scripts.clear();u.bulkPreloads.forEach(Ge,b);u.bulkPreloads.clear();var F=u.preloadChunks;for(d=0;d<F.length;d++)v(b,F[d]);F.length=0;var ma=u.hoistableChunks;for(d=0;d<ma.length;d++)v(b,ma[d]);ma.length=0;var y=a.clientRenderedBoundaries;for(c=0;c<y.length;c++){var T=y[c];u=b;var G=a.resumableState,L=a.renderState,M=T.rootSegmentID,Pa=T.errorDigest,Ba=T.errorMessage,sa=T.errorComponentStack,
na=0===G.streamingFormat;na?(v(u,L.startInlineScript),0===(G.instructions&4)?(G.instructions|=4,v(u,je)):v(u,ke)):v(u,oe);v(u,L.boundaryPrefix);v(u,z(M.toString(16)));na&&v(u,le);if(Pa||Ba||sa)na?(v(u,me),v(u,z(te(Pa||"")))):(v(u,pe),v(u,z(H(Pa||""))));if(Ba||sa)na?(v(u,me),v(u,z(te(Ba||"")))):(v(u,qe),v(u,z(H(Ba||""))));sa&&(na?(v(u,me),v(u,z(te(sa)))):(v(u,re),v(u,z(H(sa)))));if(na?!w(u,ne):!w(u,bb)){a.destination=null;c++;y.splice(0,c);return}}y.splice(0,c);var Ca=a.completedBoundaries;for(c=0;c<
Ca.length;c++)if(!ph(a,b,Ca[c])){a.destination=null;c++;Ca.splice(0,c);return}Ca.splice(0,c);fa(b);n=new Uint8Array(512);r=0;var ta=a.partialBoundaries;for(c=0;c<ta.length;c++){var ua=ta[c];a:{y=a;T=b;y.renderState.boundaryResources=ua.resources;var va=ua.completedSegments;for(G=0;G<va.length;G++)if(!qh(y,T,ua,va[G])){G++;va.splice(0,G);var Qa=!1;break a}va.splice(0,G);Qa=Fe(T,ua.resources,y.renderState)}if(!Qa){a.destination=null;c++;ta.splice(0,c);return}}ta.splice(0,c);var ia=a.completedBoundaries;
for(c=0;c<ia.length;c++)if(!ph(a,b,ia[c])){a.destination=null;c++;ia.splice(0,c);return}ia.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length?(a.flushScheduled=!1,null===a.trackedPostpones&&(c=a.resumableState,c.hasBody&&v(b,oc("body")),c.hasHtml&&v(b,oc("html"))),fa(b),b.close(),a.destination=null):fa(b)}}
function rh(a){a.flushScheduled=null!==a.destination;Qg(a);null===a.trackedPostpones&&kh(a,0===a.pendingRootTasks)}function Ye(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){a.flushScheduled=!0;var b=a.destination;b?lh(a,b):a.flushScheduled=!1}}function sh(a,b){if(1===a.status)a.status=2,ka(b,a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{lh(a,b)}catch(c){Sg(a,c),Tg(a,c)}}}
function th(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error(l(432)):b;c.forEach(function(e){return hh(e,a,d)});c.clear()}null!==a.destination&&lh(a,a.destination)}catch(e){Sg(a,e),Tg(a,e)}}function dh(a,b,c){if(null===b)c.rootNodes.push(a);else{var d=c.workingMap,e=d.get(b);void 0===e&&(e=[b[1],b[2],[],null],d.set(b,e),dh(e,b[0],c));e[2].push(a)}}
function uh(a){var b=a.trackedPostpones;if(null===b||0===b.rootNodes.length&&null===b.rootSlots)return a.trackedPostpones=null;if(null!==a.completedRootSegment&&5===a.completedRootSegment.status){var c=a.resumableState,d=a.renderState;c.nextFormID=0;c.hasBody=!1;c.hasHtml=!1;c.unknownResources={font:d.resets.font};c.dnsResources=d.resets.dns;c.connectResources=d.resets.connect;c.imageResources=d.resets.image;c.styleResources=d.resets.style;c.scriptResources={};c.moduleUnknownResources={};c.moduleScriptResources=
{}}else c=a.resumableState,c.bootstrapScriptContent=void 0,c.bootstrapScripts=void 0,c.bootstrapModules=void 0;return{nextSegmentId:a.nextSegmentId,rootFormatContext:a.rootFormatContext,progressiveChunkSize:a.progressiveChunkSize,resumableState:a.resumableState,replayNodes:b.rootNodes,replaySlots:b.rootSlots}}
exports.prerender=function(a,b){return new Promise(function(c,d){var e=b?b.onHeaders:void 0,f;e&&(f=function(p){e(new Headers(p))});var g=Lb(b?b.identifierPrefix:void 0,b?b.unstable_externalRuntimeSrc:void 0,b?b.bootstrapScriptContent:void 0,b?b.bootstrapScripts:void 0,b?b.bootstrapModules:void 0),h=Mg(a,g,Jb(g,void 0,b?b.unstable_externalRuntimeSrc:void 0,b?b.importMap:void 0,f,b?b.maxHeadersLength:void 0),Mb(b?b.namespaceURI:void 0),b?b.progressiveChunkSize:void 0,b?b.onError:void 0,function(){var p=
new ReadableStream({type:"bytes",pull:function(t){sh(h,t)},cancel:function(t){h.destination=null;th(h,t)}},{highWaterMark:0});p={postponed:uh(h),prelude:p};c(p)},void 0,void 0,d,b?b.onPostpone:void 0);if(b&&b.signal){var k=b.signal;if(k.aborted)th(h,k.reason);else{var m=function(){th(h,k.reason);k.removeEventListener("abort",m)};k.addEventListener("abort",m)}}rh(h)})};
exports.renderToReadableStream=function(a,b){return new Promise(function(c,d){var e,f,g=new Promise(function(x,A){f=x;e=A}),h=b?b.onHeaders:void 0,k;h&&(k=function(x){h(new Headers(x))});var m=Lb(b?b.identifierPrefix:void 0,b?b.unstable_externalRuntimeSrc:void 0,b?b.bootstrapScriptContent:void 0,b?b.bootstrapScripts:void 0,b?b.bootstrapModules:void 0),p=Jg(a,m,Jb(m,b?b.nonce:void 0,b?b.unstable_externalRuntimeSrc:void 0,b?b.importMap:void 0,k,b?b.maxHeadersLength:void 0),Mb(b?b.namespaceURI:void 0),
b?b.progressiveChunkSize:void 0,b?b.onError:void 0,f,function(){var x=new ReadableStream({type:"bytes",pull:function(A){sh(p,A)},cancel:function(A){p.destination=null;th(p,A)}},{highWaterMark:0});x.allReady=g;c(x)},function(x){g.catch(function(){});d(x)},e,b?b.onPostpone:void 0,b?b.formState:void 0);if(b&&b.signal){var t=b.signal;if(t.aborted)th(p,t.reason);else{var q=function(){th(p,t.reason);t.removeEventListener("abort",q)};t.addEventListener("abort",q)}}rh(p)})};
exports.resume=function(a,b,c){return new Promise(function(d,e){var f,g,h=new Promise(function(t,q){g=t;f=q}),k=Ng(a,b,Jb(b.resumableState,c?c.nonce:void 0,void 0,void 0,void 0,void 0),c?c.onError:void 0,g,function(){var t=new ReadableStream({type:"bytes",pull:function(q){sh(k,q)},cancel:function(q){k.destination=null;th(k,q)}},{highWaterMark:0});t.allReady=h;d(t)},function(t){h.catch(function(){});e(t)},f,c?c.onPostpone:void 0);if(c&&c.signal){var m=c.signal;if(m.aborted)th(k,m.reason);else{var p=
function(){th(k,m.reason);m.removeEventListener("abort",p)};m.addEventListener("abort",p)}}rh(k)})};exports.version="18.3.0-experimental-2c338b16f-20231116";

//# sourceMappingURL=react-dom-server.browser.production.min.js.map
