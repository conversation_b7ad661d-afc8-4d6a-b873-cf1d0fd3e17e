(()=>{var e={};e.id=702,e.ids=[702],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},9971:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>u,routeModule:()=>f,tree:()=>d});var n=r(6625),i=r(5345),a=r(4666),s=r.n(a),o=r(8889),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2063)),"C:\\Projects\\augment_code_v0_compare\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6013)),"C:\\Projects\\augment_code_v0_compare\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,8363)),"C:\\Projects\\augment_code_v0_compare\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,1737)),"C:\\Projects\\augment_code_v0_compare\\app\\not-found.tsx"]}],u=["C:\\Projects\\augment_code_v0_compare\\app\\dashboard\\page.tsx"],c="/dashboard/page",p={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1387:(e,t,r)=>{Promise.resolve().then(r.bind(r,417))},417:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var n=r(4353),i=r(2566),a=r(8596),s=r(948),o=r(8858),l=r.n(o);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,r(5668).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]);function u({property:e}){return(0,n.jsxs)("div",{className:"card overflow-hidden",children:[n.jsx("div",{className:"px-6 py-4 border-b border-primary-100",children:(0,n.jsxs)("h3",{className:"text-lg font-semibold text-primary-900 flex items-center justify-between",children:[e.name,n.jsx("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-primary-600 hover:text-primary-900 transition-colors",title:"Visit website",children:n.jsx(d,{size:18})})]})}),(0,n.jsxs)("div",{className:"p-6",children:[n.jsx("div",{className:"relative w-full h-48 mb-4 rounded-lg overflow-hidden",children:n.jsx(l(),{src:e.image,alt:e.name,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})}),n.jsx("p",{className:"text-primary-600 text-sm leading-relaxed",children:e.description})]}),n.jsx("div",{className:"px-6 py-4 border-t border-primary-100 bg-primary-50",children:n.jsx("div",{className:"flex justify-end",children:(0,n.jsxs)("span",{className:"text-xs text-primary-500",children:["Established ",new Date(e.establishedDate).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]})})})]})}var c=r(6520),p=r(6220);function f(){let{user:e,loading:t}=(0,c.useAuth)(),r=(0,a.useRouter)();return((0,i.useEffect)(()=>{t||e||r.push("/unauthorized")},[e,t,r]),t)?n.jsx(s.Layout,{title:"Dashboard",children:n.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,n.jsxs)("div",{className:"text-center",children:[n.jsx("div",{className:"loading-spinner w-8 h-8 mx-auto mb-4"}),n.jsx("p",{className:"text-primary-600",children:"Loading dashboard..."})]})})}):e?n.jsx(s.Layout,{title:"Dashboard",children:(0,n.jsxs)("div",{className:"space-y-8",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsxs)("p",{className:"text-lg text-primary-600",children:["Welcome to your property management dashboard, ",e.name]}),n.jsx("p",{className:"text-primary-500 mt-2",children:"Manage and access all St Cloud Enterprises website properties"})]}),n.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.$o.map(e=>n.jsx(u,{property:e},e.id))}),0===p.$o.length&&n.jsx("div",{className:"text-center py-12",children:n.jsx("p",{className:"text-primary-500",children:"No properties available at this time."})})]})}):null}},8893:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return x}});let n=r(3804),i=r(3133)._(r(2566)),a=n._(r(517)),s=n._(r(7529)),o=r(9389),l=r(614),d=r(8945);r(9569);let u=r(2755),c=n._(r(4988)),p={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function f(e,t,r,n,i,a){let s=null==e?void 0:e.src;e&&e["data-loaded-src"]!==s&&(e["data-loaded-src"]=s,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&i(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,i=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{i=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function m(e){let[t,r]=i.version.split(".",2),n=parseInt(t,10),a=parseInt(r,10);return n>18||18===n&&a>=3?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let g=(0,i.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:a,height:s,width:o,decoding:l,className:d,style:u,fetchPriority:c,placeholder:p,loading:g,unoptimized:h,fill:x,onLoadRef:y,onLoadingCompleteRef:v,setBlurComplete:b,setShowAltText:_,onLoad:j,onError:w,...P}=e;return i.default.createElement("img",{...P,...m(c),loading:g,width:o,height:s,decoding:l,"data-nimg":x?"fill":"1",className:d,style:u,sizes:a,srcSet:n,src:r,ref:(0,i.useCallback)(e=>{t&&("function"==typeof t?t(e):"object"==typeof t&&(t.current=e)),e&&(w&&(e.src=e.src),e.complete&&f(e,p,y,v,b,h))},[r,p,y,v,b,w,h,t]),onLoad:e=>{f(e.currentTarget,p,y,v,b,h)},onError:e=>{_(!0),"empty"!==p&&b(!0),w&&w(e)}})});function h(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...m(r.fetchPriority)};return t&&a.default.preload?(a.default.preload(r.src,n),null):i.default.createElement(s.default,null,i.default.createElement("link",{key:"__nimg-"+r.src+r.srcSet+r.sizes,rel:"preload",href:r.srcSet?void 0:r.src,...n}))}let x=(0,i.forwardRef)((e,t)=>{let r=(0,i.useContext)(u.RouterContext),n=(0,i.useContext)(d.ImageConfigContext),a=(0,i.useMemo)(()=>{let e=p||n||l.imageConfigDefault,t=[...e.deviceSizes,...e.imageSizes].sort((e,t)=>e-t),r=e.deviceSizes.sort((e,t)=>e-t);return{...e,allSizes:t,deviceSizes:r}},[n]),{onLoad:s,onLoadingComplete:f}=e,m=(0,i.useRef)(s);(0,i.useEffect)(()=>{m.current=s},[s]);let x=(0,i.useRef)(f);(0,i.useEffect)(()=>{x.current=f},[f]);let[y,v]=(0,i.useState)(!1),[b,_]=(0,i.useState)(!1),{props:j,meta:w}=(0,o.getImgProps)(e,{defaultLoader:c.default,imgConf:a,blurComplete:y,showAltText:b});return i.default.createElement(i.default.Fragment,null,i.default.createElement(g,{...j,unoptimized:w.unoptimized,placeholder:w.placeholder,fill:w.fill,onLoadRef:m,onLoadingCompleteRef:x,setBlurComplete:v,setShowAltText:_,ref:t}),w.priority?i.default.createElement(h,{isAppRouter:!r,imgAttributes:j}):null)});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1212:(e,t,r)=>{"use strict";e.exports=r(3101).vendored.contexts.AmpContext},4468:(e,t,r)=>{"use strict";e.exports=r(3101).vendored.contexts.HeadManagerContext},8945:(e,t,r)=>{"use strict";e.exports=r(3101).vendored.contexts.ImageConfigContext},662:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},9389:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return o}}),r(9569);let n=r(2453),i=r(614);function a(e){return void 0!==e.default}function s(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function o(e,t){var r;let o,l,d,{src:u,sizes:c,unoptimized:p=!1,priority:f=!1,loading:m,className:g,quality:h,width:x,height:y,fill:v=!1,style:b,onLoad:_,onLoadingComplete:j,placeholder:w="empty",blurDataURL:P,fetchPriority:S,layout:C,objectFit:E,objectPosition:O,lazyBoundary:M,lazyRoot:z,...N}=e,{imgConf:I,showAltText:k,blurComplete:A,defaultLoader:R}=t,D=I||i.imageConfigDefault;if("allSizes"in D)o=D;else{let e=[...D.deviceSizes,...D.imageSizes].sort((e,t)=>e-t),t=D.deviceSizes.sort((e,t)=>e-t);o={...D,allSizes:e,deviceSizes:t}}let q=N.loader||R;delete N.loader,delete N.srcSet;let L="__next_img_default"in q;if(L){if("custom"===o.loader)throw Error('Image with src "'+u+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=q;q=t=>{let{config:r,...n}=t;return e(n)}}if(C){"fill"===C&&(v=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[C];e&&(b={...b,...e});let t={responsive:"100vw",fill:"100vw"}[C];t&&!c&&(c=t)}let G="",U=s(x),T=s(y);if("object"==typeof(r=u)&&(a(r)||void 0!==r.src)){let e=a(u)?u.default:u;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(l=e.blurWidth,d=e.blurHeight,P=P||e.blurDataURL,G=e.src,!v){if(U||T){if(U&&!T){let t=U/e.width;T=Math.round(e.height*t)}else if(!U&&T){let t=T/e.height;U=Math.round(e.width*t)}}else U=e.width,T=e.height}}let B=!f&&("lazy"===m||void 0===m);(!(u="string"==typeof u?u:G)||u.startsWith("data:")||u.startsWith("blob:"))&&(p=!0,B=!1),o.unoptimized&&(p=!0),L&&u.endsWith(".svg")&&!o.dangerouslyAllowSVG&&(p=!0),f&&(S="high");let F=s(h),W=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:E,objectPosition:O}:{},k?{}:{color:"transparent"},b),V=A||"empty"===w?null:"blur"===w?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:U,heightInt:T,blurWidth:l,blurHeight:d,blurDataURL:P||"",objectFit:W.objectFit})+'")':'url("'+w+'")',$=V?{backgroundSize:W.objectFit||"cover",backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:V}:{},H=function(e){let{config:t,src:r,unoptimized:n,width:i,quality:a,sizes:s,loader:o}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,r){let{deviceSizes:n,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);n)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,s),u=l.length-1;return{sizes:s||"w"!==d?s:"100vw",srcSet:l.map((e,n)=>o({config:t,src:r,quality:a,width:e})+" "+("w"===d?e:n+1)+d).join(", "),src:o({config:t,src:r,quality:a,width:l[u]})}}({config:o,src:u,unoptimized:p,width:U,quality:F,sizes:c,loader:q});return{props:{...N,loading:B?"lazy":m,fetchPriority:S,width:U,height:T,decoding:"async",className:g,style:{...W,...$},sizes:H.sizes,srcSet:H.srcSet,src:H.src},meta:{unoptimized:p,priority:f,placeholder:w,fill:v}}}},7529:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{defaultHead:function(){return d},default:function(){return f}});let n=r(3804),i=r(3133)._(r(2566)),a=n._(r(6939)),s=r(1212),o=r(4468),l=r(662);function d(e){void 0===e&&(e=!1);let t=[i.default.createElement("meta",{charSet:"utf-8"})];return e||t.push(i.default.createElement("meta",{name:"viewport",content:"width=device-width"})),t}function u(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===i.default.Fragment?e.concat(i.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(9569);let c=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:r}=t;return e.reduce(u,[]).reverse().concat(d(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return i=>{let a=!0,s=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){s=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?a=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?a=!1:t.add(i.type);break;case"meta":for(let e=0,t=c.length;e<t;e++){let t=c[e];if(i.props.hasOwnProperty(t)){if("charSet"===t)r.has(t)?a=!1:r.add(t);else{let e=i.props[t],r=n[t]||new Set;("name"!==t||!s)&&r.has(e)?a=!1:(r.add(e),n[t]=r)}}}}return a}}()).reverse().map((e,t)=>{let n=e.key||t;if(!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,i.default.cloneElement(e,t)}return i.default.cloneElement(e,{key:n})})}let f=function(e){let{children:t}=e,r=(0,i.useContext)(s.AmpStateContext),n=(0,i.useContext)(o.HeadManagerContext);return i.default.createElement(a.default,{reduceComponentsToState:p,headManager:n,inAmpMode:(0,l.isInAmpMode)(r)},t)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2453:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:i,blurDataURL:a,objectFit:s}=e,o=n?40*n:t,l=i?40*i:r,d=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===s?"xMidYMid":"cover"===s?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+a+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},614:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1}},5629:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{unstable_getImgProps:function(){return l},default:function(){return d}});let n=r(3804),i=r(9389),a=r(9569),s=r(8893),o=n._(r(4988)),l=e=>{(0,a.warnOnce)("Warning: unstable_getImgProps() is experimental and may change or be removed at any time. Use at your own risk.");let{props:t}=(0,i.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}},d=s.Image},4988:(e,t)=>{"use strict";function r(e){let{config:t,src:r,width:n,quality:i}=e;return t.path+"?url="+encodeURIComponent(r)+"&w="+n+"&q="+(i||75)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},6939:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let n=r(2566),i=()=>{},a=()=>{};function s(e){var t;let{headManager:r,reduceComponentsToState:s}=e;function o(){if(r&&r.mountedInstances){let t=n.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(s(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),o(),i(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),i(()=>(r&&(r._pendingUpdate=o),()=>{r&&(r._pendingUpdate=o)})),a(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},9569:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},8858:(e,t,r)=>{e.exports=r(5629)},2063:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>i,default:()=>s});let n=(0,r(8164).createProxy)(String.raw`C:\Projects\augment_code_v0_compare\app\dashboard\page.tsx`),{__esModule:i,$$typeof:a}=n,s=n.default}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[36,737],()=>r(9971));module.exports=n})();