{"version": 3, "sources": ["../../../../src/build/babel/loader/index.ts"], "names": ["transform", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parentTrace", "inputSource", "inputSourceMap", "filename", "resourcePath", "target", "loaderOptions", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "getOptions", "loaderSpanInner", "code", "transformedSource", "map", "outputSourceMap", "call", "nextBabelLoaderOuter", "callback", "async", "loaderSpan", "currentTraceSpan", "traceAsyncFn", "then", "err"], "mappings": "AACA,OAAOA,eAAe,cAAa;AAGnC,eAAeC,gBAEbC,WAAiB,EACjBC,WAAmB,EACnBC,cAAyC;IAEzC,MAAMC,WAAW,IAAI,CAACC,YAAY;IAClC,MAAMC,SAAS,IAAI,CAACA,MAAM;IAC1B,MAAMC,gBAAgBN,YACnBO,UAAU,CAAC,cACZ,+DAA+D;KAC9DC,OAAO,CAAC,IAAM,IAAI,CAACC,UAAU;IAEhC,MAAMC,kBAAkBV,YAAYO,UAAU,CAAC;IAC/C,MAAM,EAAEI,MAAMC,iBAAiB,EAAEC,KAAKC,eAAe,EAAE,GACrDJ,gBAAgBF,OAAO,CAAC,IACtBV,UAAUiB,IAAI,CACZ,IAAI,EACJd,aACAC,gBACAI,eACAH,UACAE,QACAK;IAIN,OAAO;QAACE;QAAmBE;KAAgB;AAC7C;AAEA,MAAME,uBAAuB,SAASA,qBAEpCf,WAAmB,EACnBC,cAAyC;IAEzC,MAAMe,WAAW,IAAI,CAACC,KAAK;IAE3B,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACb,UAAU,CAAC;IACpDY,WACGE,YAAY,CAAC,IACZtB,gBAAgBgB,IAAI,CAAC,IAAI,EAAEI,YAAYlB,aAAaC,iBAErDoB,IAAI,CACH,CAAC,CAACV,mBAAmBE,gBAAqB,GACxCG,4BAAAA,SAAW,MAAML,mBAAmBE,mBAAmBZ,iBACzD,CAACqB;QACCN,4BAAAA,SAAWM;IACb;AAEN;AAEA,eAAeP,qBAAoB"}