(()=>{var e={};e.id=931,e.ids=[931],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},8701:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>p,pages:()=>d,routeModule:()=>x,tree:()=>l});var s=r(6625),a=r(5345),n=r(4666),o=r.n(n),i=r(8889),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3283)),"C:\\Projects\\augment_code_v0_compare\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,6013)),"C:\\Projects\\augment_code_v0_compare\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,8363)),"C:\\Projects\\augment_code_v0_compare\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,1737)),"C:\\Projects\\augment_code_v0_compare\\app\\not-found.tsx"]}],d=["C:\\Projects\\augment_code_v0_compare\\app\\page.tsx"],p="/page",m={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},516:(e,t,r)=>{Promise.resolve().then(r.bind(r,1285))},1285:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(4353),a=r(3038),n=r.n(a),o=r(948),i=r(6520),c=r(6220);function l(){let{user:e}=(0,i.useAuth)();return s.jsx(o.Layout,{children:s.jsx("div",{className:"min-h-[60vh] flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center max-w-2xl mx-auto px-4",children:[s.jsx("h1",{className:"text-4xl md:text-6xl font-bold text-primary-900 mb-8",children:e?s.jsx(n(),{href:"/dashboard",className:"hover:text-primary-700 transition-colors cursor-pointer",children:c.JA.name}):c.JA.name}),s.jsx("p",{className:"text-lg text-primary-600 mb-8 leading-relaxed",children:c.JA.missionStatement}),!e&&(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("p",{className:"text-primary-500",children:"Sign in to access your dashboard and manage website properties."}),s.jsx(n(),{href:"/signin",className:"btn-primary inline-block",children:"Get Started"})]}),e&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("p",{className:"text-primary-600",children:["Welcome back, ",e.name,"!"]}),s.jsx(n(),{href:"/dashboard",className:"btn-primary inline-block",children:"Go to Dashboard"})]})]})})})}},3283:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>o});let s=(0,r(8164).createProxy)(String.raw`C:\Projects\augment_code_v0_compare\app\page.tsx`),{__esModule:a,$$typeof:n}=s,o=s.default}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[545,653],()=>r(8701));module.exports=s})();