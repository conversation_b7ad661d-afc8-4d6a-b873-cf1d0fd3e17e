{"version": 3, "sources": ["../../../../../../src/build/webpack/plugins/terser-webpack-plugin/src/minify.ts"], "names": ["terser", "buildTerserOptions", "terserOptions", "mangle", "sourceMap", "undefined", "format", "beautify", "output", "minify", "options", "name", "input", "inputSourceMap", "opts", "asObject", "result"], "mappings": "AAAA,OAAOA,YAAY,4BAA2B;AAE9C,SAASC,mBAAmBC,gBAAqB,CAAC,CAAC;IACjD,OAAO;QACL,GAAGA,aAAa;QAChBC,QACED,cAAcC,MAAM,IAAI,OACpB,OACA,OAAOD,cAAcC,MAAM,KAAK,YAChCD,cAAcC,MAAM,GACpB;YAAE,GAAGD,cAAcC,MAAM;QAAC;QAChC,kCAAkC;QAClC,wCAAwC;QACxCC,WAAWC;QACX,oCAAoC;QACpC,GAAIH,cAAcI,MAAM,GACpB;YAAEA,QAAQ;gBAAEC,UAAU;gBAAO,GAAGL,cAAcI,MAAM;YAAC;QAAE,IACvD;YAAEE,QAAQ;gBAAED,UAAU;gBAAO,GAAGL,cAAcM,MAAM;YAAC;QAAE,CAAC;IAC9D;AACF;AAEA,OAAO,eAAeC,OAAOC,OAAY;IACvC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,cAAc,EAAEX,aAAa,EAAE,GAAGQ;IACvD,sBAAsB;IACtB,MAAMI,OAAOb,mBAAmBC;IAEhC,kCAAkC;IAClC,IAAIW,gBAAgB;QAClB,aAAa;QACbC,KAAKV,SAAS,GAAG;YAAEW,UAAU;QAAK;IACpC;IAEA,MAAMC,SAAS,MAAMhB,OAAOS,MAAM,CAAC;QAAE,CAACE,KAAK,EAAEC;IAAM,GAAGE;IACtD,OAAOE;AACT"}