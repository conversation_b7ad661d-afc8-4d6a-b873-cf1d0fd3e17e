{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-barrel-loader.ts"], "names": ["barrelTransformMappingCache", "Map", "getBarrelMapping", "resourcePath", "swcCacheDir", "resolve", "fs", "has", "get", "transpileSource", "filename", "source", "isWildcard", "isTypeScript", "endsWith", "Promise", "res", "transform", "inputSourceMap", "undefined", "sourceFileName", "optimizeBarrelExports", "wildcard", "jsc", "parser", "syntax", "experimental", "cacheRoot", "then", "output", "code", "visited", "Set", "getMatches", "file", "isClientEntry", "add", "rej", "readFile", "err", "data", "toString", "matches", "match", "matchedDirectives", "directiveList", "JSON", "parse", "includes", "exportList", "slice", "wildcardExports", "matchAll", "map", "decl", "length", "all", "req", "targetPath", "path", "dirname", "replace", "targetMatches", "concat", "set", "NextBarrelLoader", "async", "cacheable", "names", "getOptions", "getResolve", "mainFields", "mapping", "clearDependencies", "callback", "stringify", "exportMap", "name", "filePath", "orig", "missedNames", "push", "join"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoFC;;;;+BAyPD;;;eAAA;;;6DArPiB;qBACS;;;;;;AAE1B,iFAAiF;AACjF,mFAAmF;AACnF,+DAA+D;AAC/D,kEAAkE;AAClE,MAAMA,8BAA8B,IAAIC;AASxC,eAAeC,iBACbC,YAAoB,EACpBC,WAAmB,EACnBC,OAA8D,EAC9DC,EAKC;IAED,IAAIN,4BAA4BO,GAAG,CAACJ,eAAe;QACjD,OAAOH,4BAA4BQ,GAAG,CAACL;IACzC;IAEA,6EAA6E;IAC7E,mDAAmD;IACnD,eAAeM,gBACbC,QAAgB,EAChBC,MAAc,EACdC,UAAmB;QAEnB,MAAMC,eAAeH,SAASI,QAAQ,CAAC,UAAUJ,SAASI,QAAQ,CAAC;QACnE,OAAO,IAAIC,QAAgB,CAACC,MAC1BC,IAAAA,cAAS,EAACN,QAAQ;gBAChBD;gBACAQ,gBAAgBC;gBAChBC,gBAAgBV;gBAChBW,uBAAuB;oBACrBC,UAAUV;gBACZ;gBACAW,KAAK;oBACHC,QAAQ;wBACNC,QAAQZ,eAAe,eAAe;wBACtC,CAACA,eAAe,QAAQ,MAAM,EAAE;oBAClC;oBACAa,cAAc;wBACZC,WAAWvB;oBACb;gBACF;YACF,GAAGwB,IAAI,CAAC,CAACC;gBACPb,IAAIa,OAAOC,IAAI;YACjB;IAEJ;IAEA,yCAAyC;IACzC,MAAMC,UAAU,IAAIC;IACpB,eAAeC,WACbC,IAAY,EACZtB,UAAmB,EACnBuB,aAAsB;QAEtB,IAAIJ,QAAQxB,GAAG,CAAC2B,OAAO;YACrB,OAAO;QACT;QACAH,QAAQK,GAAG,CAACF;QAEZ,MAAMvB,SAAS,MAAM,IAAII,QAAgB,CAACC,KAAKqB;YAC7C/B,GAAGgC,QAAQ,CAACJ,MAAM,CAACK,KAAKC;gBACtB,IAAID,OAAOC,SAASrB,WAAW;oBAC7BkB,IAAIE;gBACN,OAAO;oBACLvB,IAAIwB,KAAKC,QAAQ;gBACnB;YACF;QACF;QAEA,MAAMZ,SAAS,MAAMpB,gBAAgByB,MAAMvB,QAAQC;QAEnD,MAAM8B,UAAUb,OAAOc,KAAK,CAC1B;QAEF,IAAI,CAACD,SAAS;YACZ,OAAO;QACT;QAEA,MAAME,oBAAoBf,OAAOc,KAAK,CACpC;QAEF,MAAME,gBAAgBD,oBAClBE,KAAKC,KAAK,CAACH,iBAAiB,CAAC,EAAE,IAC/B,EAAE;QACN,yEAAyE;QACzET,gBAAgBU,cAAcG,QAAQ,CAAC;QAEvC,IAAIC,aAAaH,KAAKC,KAAK,CAACL,OAAO,CAAC,EAAE,CAACQ,KAAK,CAAC,GAAG,CAAC;QAKjD,MAAMC,kBAAkB;eACnBtB,OAAOuB,QAAQ,CAAC;SACpB,CAACC,GAAG,CAAC,CAACV,QAAUA,KAAK,CAAC,EAAE;QAEzB,uEAAuE;QACvE,sEAAsE;QACtE,eAAe;QACf,IAAI/B,YAAY;YACd,KAAK,MAAM0C,QAAQL,WAAY;gBAC7BK,IAAI,CAAC,EAAE,GAAGpB;gBACVoB,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE;YACnB;QACF;QAEA,6EAA6E;QAC7E,IAAIH,gBAAgBI,MAAM,EAAE;YAC1B,MAAMxC,QAAQyC,GAAG,CACfL,gBAAgBE,GAAG,CAAC,OAAOI;gBACzB,MAAMC,aAAa,MAAMrD,QACvBsD,aAAI,CAACC,OAAO,CAAC1B,OACbuB,IAAII,OAAO,CAAC,gDAAgD;gBAG9D,MAAMC,gBAAgB,MAAM7B,WAC1ByB,YACA,MACAvB;gBAEF,IAAI2B,eAAe;oBACjB,wBAAwB;oBACxBb,aAAaA,WAAWc,MAAM,CAACD,cAAcb,UAAU;gBACzD;YACF;QAEJ;QAEA,OAAO;YACLA;YACAE;YACAhB;QACF;IACF;IAEA,MAAMnB,MAAM,MAAMiB,WAAW9B,cAAc,OAAO;IAClDH,4BAA4BgE,GAAG,CAAC7D,cAAca;IAE9C,OAAOA;AACT;AAEA,MAAMiD,mBAAmB;IAMvB,IAAI,CAACC,KAAK;IACV,IAAI,CAACC,SAAS,CAAC;IAEf,MAAM,EAAEC,KAAK,EAAEhE,WAAW,EAAE,GAAG,IAAI,CAACiE,UAAU;IAE9C,yEAAyE;IACzE,6EAA6E;IAC7E,MAAMhE,UAAU,IAAI,CAACiE,UAAU,CAAC;QAC9BC,YAAY;YAAC;YAAU;SAAO;IAChC;IAEA,MAAMC,UAAU,MAAMtE,iBACpB,IAAI,CAACC,YAAY,EACjBC,aACAC,SACA,IAAI,CAACC,EAAE;IAGT,4EAA4E;IAC5E,yEAAyE;IACzE,6EAA6E;IAC7E,wBAAwB;IACxB,IAAI,CAACmE,iBAAiB;IAEtB,IAAI,CAACD,SAAS;QACZ,6FAA6F;QAC7F,2FAA2F;QAC3F,0FAA0F;QAC1F,+BAA+B;QAC/B,IAAI,CAACE,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE5B,KAAK6B,SAAS,CAAC,IAAI,CAACxE,YAAY,EAAE,CAAC;QACxE;IACF;IAEA,MAAM8C,aAAauB,QAAQvB,UAAU;IACrC,MAAMd,gBAAgBqC,QAAQrC,aAAa;IAC3C,MAAMyC,YAAY,IAAI3E;IACtB,KAAK,MAAM,CAAC4E,MAAMC,UAAUC,KAAK,IAAI9B,WAAY;QAC/C2B,UAAUZ,GAAG,CAACa,MAAM;YAACC;YAAUC;SAAK;IACtC;IAEA,IAAIlD,SAAS;IACb,IAAImD,cAAwB,EAAE;IAC9B,KAAK,MAAMH,QAAQT,MAAO;QACxB,sBAAsB;QACtB,IAAIQ,UAAUrE,GAAG,CAACsE,OAAO;YACvB,MAAMvB,OAAOsB,UAAUpE,GAAG,CAACqE;YAE3B,IAAIvB,IAAI,CAAC,EAAE,KAAK,KAAK;gBACnBzB,UAAU,CAAC,cAAc,EAAEgD,KAAK,MAAM,EAAE/B,KAAK6B,SAAS,CAACrB,IAAI,CAAC,EAAE,EAAE,CAAC;YACnE,OAAO,IAAIA,IAAI,CAAC,EAAE,KAAK,WAAW;gBAChCzB,UAAU,CAAC,sBAAsB,EAAEgD,KAAK,QAAQ,EAAE/B,KAAK6B,SAAS,CAC9DrB,IAAI,CAAC,EAAE,EACP,CAAC;YACL,OAAO,IAAIA,IAAI,CAAC,EAAE,KAAKuB,MAAM;gBAC3BhD,UAAU,CAAC,WAAW,EAAEgD,KAAK,QAAQ,EAAE/B,KAAK6B,SAAS,CAACrB,IAAI,CAAC,EAAE,EAAE,CAAC;YAClE,OAAO;gBACLzB,UAAU,CAAC,WAAW,EAAEyB,IAAI,CAAC,EAAE,CAAC,IAAI,EAAEuB,KAAK,QAAQ,EAAE/B,KAAK6B,SAAS,CACjErB,IAAI,CAAC,EAAE,EACP,CAAC;YACL;QACF,OAAO;YACL0B,YAAYC,IAAI,CAACJ;QACnB;IACF;IAEA,mCAAmC;IACnC,IAAIG,YAAYzB,MAAM,GAAG,GAAG;QAC1B,KAAK,MAAME,OAAOe,QAAQrB,eAAe,CAAE;YACzCtB,UAAU,CAAC,gBAAgB,EAAEiB,KAAK6B,SAAS,CACzClB,IAAII,OAAO,CAAC,mBAAmBmB,YAAYE,IAAI,CAAC,OAAO,cACvD,CAAC;QACL;IACF;IAEA,yEAAyE;IACzE,yCAAyC;IACzC,IAAI/C,eAAe;QACjBN,SAAS,CAAC,eAAe,EAAEA,OAAO,CAAC;IACrC;IAEA,IAAI,CAAC6C,QAAQ,CAAC,MAAM7C;AACtB;MAEA,WAAeoC"}