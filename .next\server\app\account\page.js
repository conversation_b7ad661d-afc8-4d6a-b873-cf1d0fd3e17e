(()=>{var e={};e.id=346,e.ids=[346],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6920:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>o});var r=s(6625),a=s(5345),n=s(4666),i=s.n(n),c=s(8889),l={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);s.d(t,l);let o=["",{children:["account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,2125)),"C:\\Projects\\augment_code_v0_compare\\app\\account\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,6013)),"C:\\Projects\\augment_code_v0_compare\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,8363)),"C:\\Projects\\augment_code_v0_compare\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,1737)),"C:\\Projects\\augment_code_v0_compare\\app\\not-found.tsx"]}],d=["C:\\Projects\\augment_code_v0_compare\\app\\account\\page.tsx"],x="/account/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/account/page",pathname:"/account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},1507:(e,t,s)=>{Promise.resolve().then(s.bind(s,3457))},3457:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(4353),a=s(2566),n=s(8596),i=s(948),c=s(6520),l=s(7774),o=s(5668);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,o.Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),x=(0,o.Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]);function m(){let{user:e,loading:t}=(0,c.useAuth)(),s=(0,n.useRouter)();return((0,a.useEffect)(()=>{t||e||s.push("/unauthorized")},[e,t,s]),t)?r.jsx(i.Layout,{title:"Account",children:r.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"loading-spinner w-8 h-8 mx-auto mb-4"}),r.jsx("p",{className:"text-primary-600",children:"Loading account..."})]})})}):e?r.jsx(i.Layout,{title:"Account",children:r.jsx("div",{className:"max-w-2xl mx-auto",children:(0,r.jsxs)("div",{className:"card p-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[r.jsx("div",{className:"w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx(l.Z,{size:32,className:"text-primary-600"})}),r.jsx("h2",{className:"text-2xl font-bold text-primary-900",children:e.name})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-primary-50 rounded-lg",children:[r.jsx(d,{size:20,className:"text-primary-600"}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-primary-700",children:"Email Address"}),r.jsx("p",{className:"text-primary-900",children:e.email})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-primary-50 rounded-lg",children:[r.jsx(l.Z,{size:20,className:"text-primary-600"}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-primary-700",children:"User ID"}),r.jsx("p",{className:"text-primary-900",children:e.id})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-primary-50 rounded-lg",children:[r.jsx(x,{size:20,className:"text-primary-600"}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-primary-700",children:"Account Status"}),r.jsx("p",{className:"text-green-600 font-medium",children:"Active"})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 pt-8 border-t border-primary-200",children:[r.jsx("h3",{className:"text-lg font-semibold text-primary-900 mb-4",children:"Account Actions"}),(0,r.jsxs)("div",{className:"space-y-3",children:[r.jsx("button",{className:"w-full btn-secondary text-left",children:"Update Profile Information"}),r.jsx("button",{className:"w-full btn-secondary text-left",children:"Change Password"}),r.jsx("button",{className:"w-full btn-secondary text-left",children:"Notification Preferences"})]})]})]})})}):null}},2125:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>i});let r=(0,s(8164).createProxy)(String.raw`C:\Projects\augment_code_v0_compare\app\account\page.tsx`),{__esModule:a,$$typeof:n}=r,i=r.default}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[36,737],()=>s(6920));module.exports=r})();