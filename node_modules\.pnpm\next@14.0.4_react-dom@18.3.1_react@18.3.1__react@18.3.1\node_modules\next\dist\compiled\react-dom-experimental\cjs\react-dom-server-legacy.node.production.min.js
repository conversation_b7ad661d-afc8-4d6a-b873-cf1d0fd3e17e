/*
 React
 react-dom-server-legacy.node.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.


 JS Implementation of MurmurHash3 (r136) (as of May 20, 2011)

 Copyright (c) 2011 Gary Court
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 SOFTWARE.
*/
'use strict';var ca=require("next/dist/compiled/react-experimental"),ha=require("react-dom"),ia=require("stream");
function ja(a,b){var c=a.length&3;var d=a.length-c;var e=b;for(b=0;b<d;){var f=a.charCodeAt(b)&255|(a.charCodeAt(++b)&255)<<8|(a.charCodeAt(++b)&255)<<16|(a.charCodeAt(++b)&255)<<24;++b;f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295;f=f<<15|f>>>17;f=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295;e^=f;e=e<<13|e>>>19;e=5*(e&65535)+((5*(e>>>16)&65535)<<16)&4294967295;e=(e&65535)+27492+(((e>>>16)+58964&65535)<<16)}f=0;switch(c){case 3:f^=(a.charCodeAt(b+2)&255)<<
16;case 2:f^=(a.charCodeAt(b+1)&255)<<8;case 1:f^=a.charCodeAt(b)&255,f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295,f=f<<15|f>>>17,e^=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295}e^=a.length;e^=e>>>16;e=2246822507*(e&65535)+((2246822507*(e>>>16)&65535)<<16)&4294967295;e^=e>>>13;e=3266489909*(e&65535)+((3266489909*(e>>>16)&65535)<<16)&4294967295;return(e^e>>>16)>>>0}
var r=Object.assign,z=Object.prototype.hasOwnProperty,va=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),wa={},xa={};
function ya(a){if(z.call(xa,a))return!0;if(z.call(wa,a))return!1;if(va.test(a))return xa[a]=!0;wa[a]=!0;return!1}
var za=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),Aa=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ha=/["'&<>]/;
function A(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Ha.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Ia=/([A-Z])/g,Ja=/^ms-/,Ka=Array.isArray,La=ca.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ma={pending:!1,data:null,method:null,action:null},Na=ha.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,lb={prefetchDNS:Oa,preconnect:fb,preload:gb,preloadModule:hb,preinitStyle:ib,preinitScript:jb,preinitModuleScript:kb},B=[],mb=/(<\/|<)(s)(cript)/gi;function nb(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}
function ob(a,b,c,d,e){var f=0;void 0!==b&&(f=1);return{idPrefix:void 0===a?"":a,nextFormID:0,streamingFormat:f,bootstrapScriptContent:c,bootstrapScripts:d,bootstrapModules:e,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}}}function F(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function pb(a){return F("http://www.w3.org/2000/svg"===a?3:"http://www.w3.org/1998/Math/MathML"===a?4:0,null,0)}
function Ab(a,b,c){switch(b){case "noscript":return F(2,null,a.tagScope|1);case "select":return F(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return F(3,null,a.tagScope);case "picture":return F(2,null,a.tagScope|2);case "math":return F(4,null,a.tagScope);case "foreignObject":return F(2,null,a.tagScope);case "table":return F(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return F(6,null,a.tagScope);case "colgroup":return F(8,null,a.tagScope);case "tr":return F(7,null,a.tagScope)}return 5<=
a.insertionMode?F(2,null,a.tagScope):0===a.insertionMode?"html"===b?F(1,null,a.tagScope):F(2,null,a.tagScope):1===a.insertionMode?F(2,null,a.tagScope):a}var Bb=new Map;
function Cb(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(z.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=A(d);e=A((""+e).trim())}else f=Bb.get(d),void 0===f&&(f=A(d.replace(Ia,"-$1").toLowerCase().replace(Ja,"-ms-")),Bb.set(d,f)),e="number"===typeof e?0===e||za.has(d)?""+e:e+"px":
A((""+e).trim());c?(c=!1,a.push(' style="',f,":",e)):a.push(";",f,":",e)}}c||a.push('"')}function Db(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""')}function I(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(" ",b,'="',A(c),'"')}function Eb(a){var b=a.nextFormID++;return a.idPrefix+b}var Fb=A("javascript:throw new Error('A React form was unexpectedly submitted.')");
function Gb(a,b){this.push('<input type="hidden"');if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");I(this,"name",b);I(this,"value",a);this.push("/>")}
function Hb(a,b,c,d,e,f,g,h){var k=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=Eb(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,k=b.data):(a.push(" ","formAction",'="',Fb,'"'),g=f=e=d=h=null,Ib(b,c)));null!=h&&J(a,"name",h);null!=d&&J(a,"formAction",d);null!=e&&J(a,"formEncType",e);null!=f&&J(a,"formMethod",f);null!=g&&J(a,"formTarget",g);return k}
function J(a,b,c){switch(b){case "className":I(a,"class",c);break;case "tabIndex":I(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":I(a,b,c);break;case "style":Cb(a,c);break;case "src":case "href":if(""===c)break;case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ",b,'="',A(""+c),'"');break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":Db(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ","xlink:href",'="',A(""+c),'"');break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',A(c),'"');break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""');break;case "capture":case "download":!0===c?a.push(" ",b,'=""'):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',A(c),'"');break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(" ",b,'="',A(c),'"');break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(" ",b,'="',A(c),'"');break;case "xlinkActuate":I(a,"xlink:actuate",
c);break;case "xlinkArcrole":I(a,"xlink:arcrole",c);break;case "xlinkRole":I(a,"xlink:role",c);break;case "xlinkShow":I(a,"xlink:show",c);break;case "xlinkTitle":I(a,"xlink:title",c);break;case "xlinkType":I(a,"xlink:type",c);break;case "xmlBase":I(a,"xml:base",c);break;case "xmlLang":I(a,"xml:lang",c);break;case "xmlSpace":I(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=Aa.get(b)||b,ya(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(" ",b,'="',A(c),'"')}}}function L(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}
function Jb(a){var b="";ca.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}
function Ib(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,'addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'A React form was unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.getRootNode(),(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,\nd,b))}});',"\x3c/script>"))}
function Kb(a,b,c,d,e,f,g){var h=b.rel,k=b.href,l=b.precedence;if(3===f||g||null!=b.itemProp||"string"!==typeof h||"string"!==typeof k||""===k)return M(a,b),null;if("stylesheet"===b.rel){if("string"!==typeof l||null!=b.disabled||b.onLoad||b.onError)return M(a,b);f=d.styles.get(l);g=c.styleResources.hasOwnProperty(k)?c.styleResources[k]:void 0;null!==g?(c.styleResources[k]=null,f||(f={precedence:A(l),rules:[],hrefs:[],sheets:new Map},d.styles.set(l,f)),b={state:0,props:r({},b,{"data-precedence":b.precedence,
precedence:null})},g&&(2===g.length&&Lb(b.props,g),(c=d.preloads.stylesheets.get(k))&&0<c.length?c.length=0:b.state=1),f.sheets.set(k,b),d.boundaryResources&&d.boundaryResources.stylesheets.add(b)):f&&(k=f.sheets.get(k))&&d.boundaryResources&&d.boundaryResources.stylesheets.add(k);e&&a.push("\x3c!-- --\x3e");return null}if(b.onLoad||b.onError)return M(a,b);e&&a.push("\x3c!-- --\x3e");switch(b.rel){case "preconnect":case "dns-prefetch":return M(d.preconnectChunks,b);case "preload":return M(d.preloadChunks,
b);default:return M(d.hoistableChunks,b)}}function M(a,b){a.push(N("link"));for(var c in b)if(z.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:J(a,c,d)}}a.push("/>");return null}
function Mb(a,b,c){a.push(N(c));for(var d in b)if(z.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:J(a,d,e)}}a.push("/>");return null}
function Nb(a,b){a.push(N("title"));var c=null,d=null,e;for(e in b)if(z.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:J(a,e,f)}}a.push(">");b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(A(""+b));L(a,d,c);a.push(Ob("title"));return null}
function Pb(a,b){a.push(N("script"));var c=null,d=null,e;for(e in b)if(z.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:J(a,e,f)}}a.push(">");L(a,d,c);"string"===typeof c&&a.push(A(c));a.push(Ob("script"));return null}
function Qb(a,b,c){a.push(N(c));var d=c=null,e;for(e in b)if(z.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:J(a,e,f)}}a.push(">");L(a,d,c);return"string"===typeof c?(a.push(A(c)),null):c}var Rb=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Sb=new Map;function N(a){var b=Sb.get(a);if(void 0===b){if(!Rb.test(a))throw Error("Invalid tag: "+a);b="<"+a;Sb.set(a,b)}return b}
function Tb(a,b,c,d,e,f,g){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(N("select"));var h=null,k=null,l;for(l in c)if(z.call(c,l)){var n=c[l];if(null!=n)switch(l){case "children":h=n;break;case "dangerouslySetInnerHTML":k=n;break;case "defaultValue":case "value":break;default:J(a,l,n)}}a.push(">");L(a,k,h);return h;case "option":var q=f.selectedValue;a.push(N("option"));var m=null,w=null,C=null,O=null,v;for(v in c)if(z.call(c,
v)){var t=c[v];if(null!=t)switch(v){case "children":m=t;break;case "selected":C=t;break;case "dangerouslySetInnerHTML":O=t;break;case "value":w=t;default:J(a,v,t)}}if(null!=q){var p=null!==w?""+w:Jb(m);if(Ka(q))for(var G=0;G<q.length;G++){if(""+q[G]===p){a.push(' selected=""');break}}else""+q===p&&a.push(' selected=""')}else C&&a.push(' selected=""');a.push(">");L(a,O,m);return m;case "textarea":a.push(N("textarea"));var u=null,x=null,E=null,D;for(D in c)if(z.call(c,D)){var y=c[D];if(null!=y)switch(D){case "children":E=
y;break;case "value":u=y;break;case "defaultValue":x=y;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");default:J(a,D,y)}}null===u&&null!==x&&(u=x);a.push(">");if(null!=E){if(null!=u)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Ka(E)){if(1<E.length)throw Error("<textarea> can only have at most one child.");u=""+E[0]}u=""+E}"string"===typeof u&&"\n"===u[0]&&a.push("\n");null!==u&&a.push(A(""+u));
return null;case "input":a.push(N("input"));var Pa=null,qb=null,Ba=null,ka=null,da=null,W=null,Qa=null,Ra=null,Sa=null,la;for(la in c)if(z.call(c,la)){var Q=c[la];if(null!=Q)switch(la){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":Pa=Q;break;case "formAction":qb=Q;break;case "formEncType":Ba=Q;break;case "formMethod":ka=Q;break;case "formTarget":da=Q;break;case "defaultChecked":Sa=
Q;break;case "defaultValue":Qa=Q;break;case "checked":Ra=Q;break;case "value":W=Q;break;default:J(a,la,Q)}}var rb=Hb(a,d,e,qb,Ba,ka,da,Pa);null!==Ra?Db(a,"checked",Ra):null!==Sa&&Db(a,"checked",Sa);null!==W?J(a,"value",W):null!==Qa&&J(a,"value",Qa);a.push("/>");null!==rb&&rb.forEach(Gb,a);return null;case "button":a.push(N("button"));var ma=null,na=null,aa=null,oa=null,pa=null,Ta=null,qa=null,Ua;for(Ua in c)if(z.call(c,Ua)){var ba=c[Ua];if(null!=ba)switch(Ua){case "children":ma=ba;break;case "dangerouslySetInnerHTML":na=
ba;break;case "name":aa=ba;break;case "formAction":oa=ba;break;case "formEncType":pa=ba;break;case "formMethod":Ta=ba;break;case "formTarget":qa=ba;break;default:J(a,Ua,ba)}}var Sc=Hb(a,d,e,oa,pa,Ta,qa,aa);a.push(">");null!==Sc&&Sc.forEach(Gb,a);L(a,na,ma);if("string"===typeof ma){a.push(A(ma));var Tc=null}else Tc=ma;return Tc;case "form":a.push(N("form"));var Va=null,Uc=null,ea=null,Wa=null,Xa=null,Ya=null,Za;for(Za in c)if(z.call(c,Za)){var fa=c[Za];if(null!=fa)switch(Za){case "children":Va=fa;
break;case "dangerouslySetInnerHTML":Uc=fa;break;case "action":ea=fa;break;case "encType":Wa=fa;break;case "method":Xa=fa;break;case "target":Ya=fa;break;default:J(a,Za,fa)}}var Yb=null,Zb=null;if("function"===typeof ea)if("function"===typeof ea.$$FORM_ACTION){var Ee=Eb(d),Ca=ea.$$FORM_ACTION(Ee);ea=Ca.action||"";Wa=Ca.encType;Xa=Ca.method;Ya=Ca.target;Yb=Ca.data;Zb=Ca.name}else a.push(" ","action",'="',Fb,'"'),Ya=Xa=Wa=ea=null,Ib(d,e);null!=ea&&J(a,"action",ea);null!=Wa&&J(a,"encType",Wa);null!=
Xa&&J(a,"method",Xa);null!=Ya&&J(a,"target",Ya);a.push(">");null!==Zb&&(a.push('<input type="hidden"'),I(a,"name",Zb),a.push("/>"),null!==Yb&&Yb.forEach(Gb,a));L(a,Uc,Va);if("string"===typeof Va){a.push(A(Va));var Vc=null}else Vc=Va;return Vc;case "menuitem":a.push(N("menuitem"));for(var sb in c)if(z.call(c,sb)){var Wc=c[sb];if(null!=Wc)switch(sb){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");default:J(a,sb,Wc)}}a.push(">");
return null;case "title":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Xc=Nb(a,c);else Nb(e.hoistableChunks,c),Xc=null;return Xc;case "link":return Kb(a,c,d,e,g,f.insertionMode,!!(f.tagScope&1));case "script":var $b=c.async;if("string"!==typeof c.src||!c.src||!$b||"function"===typeof $b||"symbol"===typeof $b||c.onLoad||c.onError||3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Yc=Pb(a,c);else{var tb=c.src;if("module"===c.type){var ub=d.moduleScriptResources;var Zc=e.preloads.moduleScripts}else ub=
d.scriptResources,Zc=e.preloads.scripts;var vb=ub.hasOwnProperty(tb)?ub[tb]:void 0;if(null!==vb){ub[tb]=null;var ac=c;if(vb){2===vb.length&&(ac=r({},c),Lb(ac,vb));var $c=Zc.get(tb);$c&&($c.length=0)}var ad=[];e.scripts.add(ad);Pb(ad,ac)}g&&a.push("\x3c!-- --\x3e");Yc=null}return Yc;case "style":var wb=c.precedence,ra=c.href;if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp||"string"!==typeof wb||"string"!==typeof ra||""===ra){a.push(N("style"));var Da=null,bd=null,$a;for($a in c)if(z.call(c,
$a)){var xb=c[$a];if(null!=xb)switch($a){case "children":Da=xb;break;case "dangerouslySetInnerHTML":bd=xb;break;default:J(a,$a,xb)}}a.push(">");var ab=Array.isArray(Da)?2>Da.length?Da[0]:null:Da;"function"!==typeof ab&&"symbol"!==typeof ab&&null!==ab&&void 0!==ab&&a.push(A(""+ab));L(a,bd,Da);a.push(Ob("style"));var cd=null}else{var sa=e.styles.get(wb);if(null!==(d.styleResources.hasOwnProperty(ra)?d.styleResources[ra]:void 0)){d.styleResources[ra]=null;sa?sa.hrefs.push(A(ra)):(sa={precedence:A(wb),
rules:[],hrefs:[A(ra)],sheets:new Map},e.styles.set(wb,sa));var dd=sa.rules,Ea=null,ed=null,yb;for(yb in c)if(z.call(c,yb)){var bc=c[yb];if(null!=bc)switch(yb){case "children":Ea=bc;break;case "dangerouslySetInnerHTML":ed=bc}}var bb=Array.isArray(Ea)?2>Ea.length?Ea[0]:null:Ea;"function"!==typeof bb&&"symbol"!==typeof bb&&null!==bb&&void 0!==bb&&dd.push(A(""+bb));L(dd,ed,Ea)}sa&&e.boundaryResources&&e.boundaryResources.styles.add(sa);g&&a.push("\x3c!-- --\x3e");cd=void 0}return cd;case "meta":if(3===
f.insertionMode||f.tagScope&1||null!=c.itemProp)var fd=Mb(a,c,"meta");else g&&a.push("\x3c!-- --\x3e"),fd="string"===typeof c.charSet?Mb(e.charsetChunks,c,"meta"):"viewport"===c.name?Mb(e.preconnectChunks,c,"meta"):Mb(e.hoistableChunks,c,"meta");return fd;case "listing":case "pre":a.push(N(b));var cb=null,db=null,eb;for(eb in c)if(z.call(c,eb)){var zb=c[eb];if(null!=zb)switch(eb){case "children":cb=zb;break;case "dangerouslySetInnerHTML":db=zb;break;default:J(a,eb,zb)}}a.push(">");if(null!=db){if(null!=
cb)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof db||!("__html"in db))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");var ta=db.__html;null!==ta&&void 0!==ta&&("string"===typeof ta&&0<ta.length&&"\n"===ta[0]?a.push("\n",ta):a.push(""+ta))}"string"===typeof cb&&"\n"===cb[0]&&a.push("\n");return cb;case "img":var K=c.src,
H=c.srcSet;if(!("lazy"===c.loading||!K&&!H||"string"!==typeof K&&null!=K||"string"!==typeof H&&null!=H)&&"low"!==c.fetchPriority&&!1===!!(f.tagScope&2)&&("string"!==typeof K||":"!==K[4]||"d"!==K[0]&&"D"!==K[0]||"a"!==K[1]&&"A"!==K[1]||"t"!==K[2]&&"T"!==K[2]||"a"!==K[3]&&"A"!==K[3])&&("string"!==typeof H||":"!==H[4]||"d"!==H[0]&&"D"!==H[0]||"a"!==H[1]&&"A"!==H[1]||"t"!==H[2]&&"T"!==H[2]||"a"!==H[3]&&"A"!==H[3])){var gd="string"===typeof c.sizes?c.sizes:void 0,Fa=H?H+"\n"+(gd||""):K,cc=e.preloads.images,
ua=cc.get(Fa);if(ua){if("high"===c.fetchPriority||10>e.highImagePreloads.size)cc.delete(Fa),e.highImagePreloads.add(ua)}else if(!d.imageResources.hasOwnProperty(Fa)){d.imageResources[Fa]=B;var dc=c.crossOrigin;var hd="string"===typeof dc?"use-credentials"===dc?dc:"":void 0;var X=e.headers,ec;X&&0<X.remainingCapacity&&("high"===c.fetchPriority||500>X.highImagePreloads.length)&&(ec=Ub(K,"image",{imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:hd,integrity:c.integrity,nonce:c.nonce,type:c.type,fetchPriority:c.fetchPriority,
referrerPolicy:c.refererPolicy}),2<=(X.remainingCapacity-=ec.length))?(e.resets.image[Fa]=B,X.highImagePreloads&&(X.highImagePreloads+=", "),X.highImagePreloads+=ec):(ua=[],M(ua,{rel:"preload",as:"image",href:H?void 0:K,imageSrcSet:H,imageSizes:gd,crossOrigin:hd,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(ua):(e.bulkPreloads.add(ua),cc.set(Fa,ua)))}}return Mb(a,c,"img");
case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return Mb(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;case "head":if(2>f.insertionMode&&null===e.headChunks){e.headChunks=[];var id=Qb(e.headChunks,c,"head")}else id=Qb(a,c,"head");return id;case "html":if(0===f.insertionMode&&null===
e.htmlChunks){e.htmlChunks=[""];var jd=Qb(e.htmlChunks,c,"html")}else jd=Qb(a,c,"html");return jd;default:if(-1!==b.indexOf("-")){a.push(N(b));var fc=null,kd=null,Ga;for(Ga in c)if(z.call(c,Ga)){var S=c[Ga];if(null!=S){var ld=Ga;switch(Ga){case "children":fc=S;break;case "dangerouslySetInnerHTML":kd=S;break;case "style":Cb(a,S);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;case "className":ld="class";default:if(ya(Ga)&&"function"!==typeof S&&"symbol"!==typeof S&&
!1!==S){if(!0===S)S="";else if("object"===typeof S)continue;a.push(" ",ld,'="',A(S),'"')}}}}a.push(">");L(a,kd,fc);return fc}}return Qb(a,c,b)}var Vb=new Map;function Ob(a){var b=Vb.get(a);void 0===b&&(b="</"+a+">",Vb.set(a,b));return b}function Wb(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)a.push(b[c]);return c<b.length?(c=b[c],b.length=0,a.push(c)):!0}
function Xb(a,b,c){a.push('\x3c!--$?--\x3e<template id="');if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");a.push(b.boundaryPrefix);b=c.toString(16);a.push(b);return a.push('"></template>')}
function gc(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return a.push('<div hidden id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 3:return a.push('<svg aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 4:return a.push('<math aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 5:return a.push('<table hidden id="'),a.push(b.segmentPrefix),
b=d.toString(16),a.push(b),a.push('">');case 6:return a.push('<table hidden><tbody id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 7:return a.push('<table hidden><tr id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 8:return a.push('<table hidden><colgroup id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');default:throw Error("Unknown insertion mode. This is a bug in React.");}}
function hc(a,b){switch(b.insertionMode){case 0:case 1:case 2:return a.push("</div>");case 3:return a.push("</svg>");case 4:return a.push("</math>");case 5:return a.push("</table>");case 6:return a.push("</tbody></table>");case 7:return a.push("</tr></table>");case 8:return a.push("</colgroup></table>");default:throw Error("Unknown insertion mode. This is a bug in React.");}}var ic=/[<\u2028\u2029]/g;
function jc(a){return JSON.stringify(a).replace(ic,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var kc=/[&><\u2028\u2029]/g;
function lc(a){return JSON.stringify(a).replace(kc,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var mc=!1,nc=!0;
function oc(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){this.push('<style media="not all" data-precedence="');this.push(a.precedence);for(this.push('" data-href="');d<c.length-1;d++)this.push(c[d]),this.push(" ");this.push(c[d]);this.push('">');for(d=0;d<b.length;d++)this.push(b[d]);nc=this.push("</style>");mc=!0;b.length=0;c.length=0}}function pc(a){return 2!==a.state?mc=!0:!1}function qc(a,b,c){mc=!1;nc=!0;b.styles.forEach(oc,a);b.stylesheets.forEach(pc);mc&&(c.stylesToHoist=!0);return nc}
function P(a){for(var b=0;b<a.length;b++)this.push(a[b]);a.length=0}var rc=[];function sc(a){M(rc,a.props);for(var b=0;b<rc.length;b++)this.push(rc[b]);rc.length=0;a.state=2}
function tc(a){var b=0<a.sheets.size;a.sheets.forEach(sc,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){this.push('<style data-precedence="');this.push(a.precedence);a=0;if(d.length){for(this.push('" data-href="');a<d.length-1;a++)this.push(d[a]),this.push(" ");this.push(d[a])}this.push('">');for(a=0;a<c.length;a++)this.push(c[a]);this.push("</style>");c.length=0;d.length=0}}
function uc(a){if(0===a.state){a.state=1;var b=a.props;M(rc,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<rc.length;a++)this.push(rc[a]);rc.length=0}}function vc(a){a.sheets.forEach(uc,this);a.sheets.clear()}
function wc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=lc(""+d.props.href),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=lc(""+d.props.href);a.push(g);e=""+e;a.push(",");e=lc(e);a.push(e);for(var h in f)if(z.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!ya(h))break a;g=""+g}e.push(",");k=lc(k);e.push(k);e.push(",");g=
lc(g);e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}
function xc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=A(JSON.stringify(""+d.props.href)),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=A(JSON.stringify(""+d.props.href));a.push(g);e=""+e;a.push(",");e=A(JSON.stringify(e));a.push(e);for(var h in f)if(z.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!ya(h))break a;g=""+g}e.push(",");k=A(JSON.stringify(k));e.push(k);
e.push(",");g=A(JSON.stringify(g));e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}
function Oa(a){var b=R?R:null;if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){c.dnsResources[a]=null;c=d.headers;var e,f;if(f=c&&0<c.remainingCapacity)f=(e="<"+(""+a).replace(yc,zc)+">; rel=dns-prefetch",2<=(c.remainingCapacity-=e.length));f?(d.resets.dns[a]=null,c.preconnects&&(c.preconnects+=", "),c.preconnects+=e):(e=[],M(e,{href:a,rel:"dns-prefetch"}),d.preconnects.add(e))}Ac(b)}}}
function fb(a,b){var c=R?R:null;if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){var f="use-credentials"===b?"credentials":"string"===typeof b?"anonymous":"default";if(!d.connectResources[f].hasOwnProperty(a)){d.connectResources[f][a]=null;d=e.headers;var g,h;if(h=d&&0<d.remainingCapacity){h="<"+(""+a).replace(yc,zc)+">; rel=preconnect";if("string"===typeof b){var k=(""+b).replace(Bc,Cc);h+='; crossorigin="'+k+'"'}h=(g=h,2<=(d.remainingCapacity-=g.length))}h?(e.resets.connect[f][a]=
null,d.preconnects&&(d.preconnects+=", "),d.preconnects+=g):(f=[],M(f,{rel:"preconnect",href:a,crossOrigin:b}),e.preconnects.add(f))}Ac(c)}}}
function gb(a,b,c){var d=R?R:null;if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var k=c.fetchPriority}var l=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(l))return;e.imageResources[l]=B;e=f.headers;var n;e&&0<e.remainingCapacity&&"high"===k&&(n=Ub(a,b,c),2<=(e.remainingCapacity-=n.length))?(f.resets.image[l]=B,e.highImagePreloads&&(e.highImagePreloads+=", "),e.highImagePreloads+=n):(e=[],M(e,r({rel:"preload",href:g?
void 0:a,as:b},c)),"high"===k?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(l,e)));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];M(g,r({rel:"preload",href:a,as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?B:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);
M(g,r({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?B:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;g[a]=B;if((e=f.headers)&&0<e.remainingCapacity&&"font"===b&&(l=Ub(a,b,c),2<=(e.remainingCapacity-=l.length)))f.resets.font[a]=B,e.fontPreloads&&(e.fontPreloads+=", "),e.fontPreloads+=l;else switch(e=[],
a=r({rel:"preload",href:a,as:b},c),M(e,a),b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}}Ac(d)}}}
function hb(a,b){var c=R?R:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?B:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=B}M(f,r({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);Ac(c)}}}
function ib(a,b,c){var d=R?R:null;if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:A(b),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:r({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&Lb(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),Ac(d))}}}
function jb(a,b){var c=R?R:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=r({src:a,async:!0},b),f&&(2===f.length&&Lb(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Pb(a,b),Ac(c))}}}
function kb(a,b){var c=R?R:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=r({src:a,type:"module",async:!0},b),f&&(2===f.length&&Lb(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Pb(a,b),Ac(c))}}}function Lb(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}
function Ub(a,b,c){a=(""+a).replace(yc,zc);b=(""+b).replace(Bc,Cc);b="<"+a+'>; rel=preload; as="'+b+'"';for(var d in c)z.call(c,d)&&(a=c[d],"string"===typeof a&&(b+="; "+d.toLowerCase()+'="'+(""+a).replace(Bc,Cc)+'"'));return b}var yc=/[<>\r\n]/g;
function zc(a){switch(a){case "<":return"%3C";case ">":return"%3E";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeLinkHrefForHeaderContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}var Bc=/["';,\r\n]/g;
function Cc(a){switch(a){case '"':return"%22";case "'":return"%27";case ";":return"%3B";case ",":return"%2C";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeStringForLinkHeaderQuotedParamValueContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}function Dc(a){this.styles.add(a)}function Ec(a){this.stylesheets.add(a)}
function Fc(a,b){var c=a.idPrefix,d=[],e=a.bootstrapScriptContent,f=a.bootstrapScripts,g=a.bootstrapModules;void 0!==e&&d.push("<script>",(""+e).replace(mb,nb),"\x3c/script>");e=c+"P:";var h=c+"S:";c+="B:";var k=new Set,l=new Set,n=new Set,q=new Map,m=new Set,w=new Set,C=new Set,O={images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map};if(void 0!==f)for(var v=0;v<f.length;v++){var t=f[v],p,G=void 0,u=void 0,x={rel:"preload",as:"script",fetchPriority:"low",nonce:void 0};"string"===
typeof t?x.href=p=t:(x.href=p=t.src,x.integrity=u="string"===typeof t.integrity?t.integrity:void 0,x.crossOrigin=G="string"===typeof t||null==t.crossOrigin?void 0:"use-credentials"===t.crossOrigin?"use-credentials":"");t=a;var E=p;t.scriptResources[E]=null;t.moduleScriptResources[E]=null;t=[];M(t,x);m.add(t);d.push('<script src="',A(p));"string"===typeof u&&d.push('" integrity="',A(u));"string"===typeof G&&d.push('" crossorigin="',A(G));d.push('" async="">\x3c/script>')}if(void 0!==g)for(f=0;f<g.length;f++)x=
g[f],G=p=void 0,u={rel:"modulepreload",fetchPriority:"low",nonce:void 0},"string"===typeof x?u.href=v=x:(u.href=v=x.src,u.integrity=G="string"===typeof x.integrity?x.integrity:void 0,u.crossOrigin=p="string"===typeof x||null==x.crossOrigin?void 0:"use-credentials"===x.crossOrigin?"use-credentials":""),x=a,t=v,x.scriptResources[t]=null,x.moduleScriptResources[t]=null,x=[],M(x,u),m.add(x),d.push('<script type="module" src="',A(v)),"string"===typeof G&&d.push('" integrity="',A(G)),"string"===typeof p&&
d.push('" crossorigin="',A(p)),d.push('" async="">\x3c/script>');return{placeholderPrefix:e,segmentPrefix:h,boundaryPrefix:c,startInlineScript:"<script>",htmlChunks:null,headChunks:null,externalRuntimeScript:null,bootstrapChunks:d,onHeaders:void 0,headers:null,resets:{font:{},dns:{},connect:{default:{},anonymous:{},credentials:{}},image:{},style:{}},charsetChunks:[],preconnectChunks:[],importMapChunks:[],preloadChunks:[],hoistableChunks:[],preconnects:k,fontPreloads:l,highImagePreloads:n,styles:q,
bootstrapScripts:m,scripts:w,bulkPreloads:C,preloads:O,boundaryResources:null,stylesToHoist:!1,generateStaticMarkup:b}}function Gc(a,b,c,d){if(c.generateStaticMarkup)return a.push(A(b)),!1;""===b?a=d:(d&&a.push("\x3c!-- --\x3e"),a.push(A(b)),a=!0);return a}
var Hc=Symbol.for("react.element"),Ic=Symbol.for("react.portal"),Jc=Symbol.for("react.fragment"),Kc=Symbol.for("react.strict_mode"),Lc=Symbol.for("react.profiler"),Mc=Symbol.for("react.provider"),Nc=Symbol.for("react.context"),Oc=Symbol.for("react.server_context"),Pc=Symbol.for("react.forward_ref"),Qc=Symbol.for("react.suspense"),Rc=Symbol.for("react.suspense_list"),md=Symbol.for("react.memo"),nd=Symbol.for("react.lazy"),od=Symbol.for("react.scope"),pd=Symbol.for("react.debug_trace_mode"),qd=Symbol.for("react.offscreen"),
rd=Symbol.for("react.legacy_hidden"),sd=Symbol.for("react.cache"),td=Symbol.for("react.default_value"),ud=Symbol.for("react.memo_cache_sentinel"),vd=Symbol.for("react.postpone"),wd=Symbol.iterator;
function xd(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case Jc:return"Fragment";case Ic:return"Portal";case Lc:return"Profiler";case Kc:return"StrictMode";case Qc:return"Suspense";case Rc:return"SuspenseList";case sd:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case Nc:return(a.displayName||"Context")+".Consumer";case Mc:return(a._context.displayName||"Context")+".Provider";case Pc:var b=a.render;a=a.displayName;
a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case md:return b=a.displayName||null,null!==b?b:xd(a.type)||"Memo";case nd:b=a._payload;a=a._init;try{return xd(a(b))}catch(c){break}case Oc:return(a.displayName||a._globalName)+".Provider"}return null}var yd={};function zd(a,b){a=a.contextTypes;if(!a)return yd;var c={},d;for(d in a)c[d]=b[d];return c}var Ad=null;
function Bd(a,b){if(a!==b){a.context._currentValue2=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");Bd(a,c)}b.context._currentValue2=b.value}}function Cd(a){a.context._currentValue2=a.parentValue;a=a.parent;null!==a&&Cd(a)}
function Dd(a){var b=a.parent;null!==b&&Dd(b);a.context._currentValue2=a.value}function Ed(a,b){a.context._currentValue2=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?Bd(a,b):Ed(a,b)}
function Fd(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?Bd(a,c):Fd(a,c);b.context._currentValue2=b.value}function Gd(a){var b=Ad;b!==a&&(null===b?Dd(a):null===a?Cd(b):b.depth===a.depth?Bd(b,a):b.depth>a.depth?Ed(b,a):Fd(b,a),Ad=a)}
var Hd={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function Id(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=Hd;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue2:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:r({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&Hd.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=r({},f,h)):r(f,h))}a.state=f}else f.queue=null}
var Jd={id:1,overflow:""};function Kd(a,b,c){var d=a.id;a=a.overflow;var e=32-Ld(d)-1;d&=~(1<<e);c+=1;var f=32-Ld(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-Ld(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var Ld=Math.clz32?Math.clz32:Md,Nd=Math.log,Od=Math.LN2;function Md(a){a>>>=0;return 0===a?32:31-(Nd(a)/Od|0)|0}var Pd=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Qd(){}function Rd(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Qd,Qd),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Sd=b;throw Pd;}}var Sd=null;
function Td(){if(null===Sd)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Sd;Sd=null;return a}function Ud(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var Vd="function"===typeof Object.is?Object.is:Ud,T=null,Wd=null,Xd=null,Yd=null,Zd=null,U=null,$d=!1,ae=!1,be=0,ce=0,de=-1,ee=0,fe=null,ge=null,he=0;
function ie(){if(null===T)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return T}
function je(){if(0<he)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function ke(){null===U?null===Zd?($d=!1,Zd=U=je()):($d=!0,U=Zd):null===U.next?($d=!1,U=U.next=je()):($d=!0,U=U.next);return U}function le(a,b,c,d){for(;ae;)ae=!1,ce=be=0,de=-1,ee=0,he+=1,U=null,c=a(b,d);me();return c}function ne(){var a=fe;fe=null;return a}function me(){Yd=Xd=Wd=T=null;ae=!1;Zd=null;he=0;U=ge=null}
function oe(a,b){return"function"===typeof b?b(a):b}function pe(a,b,c){T=ie();U=ke();if($d){var d=U.queue;b=d.dispatch;if(null!==ge&&(c=ge.get(d),void 0!==c)){ge.delete(d);d=U.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);U.memoizedState=d;return[d,b]}return[U.memoizedState,b]}a=a===oe?"function"===typeof b?b():b:void 0!==c?c(b):b;U.memoizedState=a;a=U.queue={last:null,dispatch:null};a=a.dispatch=qe.bind(null,T,a);return[U.memoizedState,a]}
function re(a,b){T=ie();U=ke();b=void 0===b?null:b;if(null!==U){var c=U.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!Vd(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();U.memoizedState=[a,b];return a}
function qe(a,b,c){if(25<=he)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===T)if(ae=!0,a={action:c,next:null},null===ge&&(ge=new Map),c=ge.get(b),void 0===c)ge.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function se(){throw Error("A function wrapped in useEffectEvent can't be called during rendering.");}function te(){throw Error("startTransition cannot be called during server rendering.");}
function ue(){throw Error("Cannot update optimistic state while rendering.");}function ve(a){var b=ee;ee+=1;null===fe&&(fe=[]);return Rd(fe,a,b)}function we(){throw Error("Cache cannot be refreshed during server rendering.");}function xe(){}
var ze={readContext:function(a){return a._currentValue2},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return ve(a);if(a.$$typeof===Nc||a.$$typeof===Oc)return a._currentValue2}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){ie();return a._currentValue2},useMemo:re,useReducer:pe,useRef:function(a){T=ie();U=ke();var b=U.memoizedState;return null===b?(a={current:a},U.memoizedState=a):b},useState:function(a){return pe(oe,a)},
useInsertionEffect:xe,useLayoutEffect:xe,useCallback:function(a,b){return re(function(){return a},b)},useImperativeHandle:xe,useEffect:xe,useDebugValue:xe,useDeferredValue:function(a,b){ie();return void 0!==b?b:a},useTransition:function(){ie();return[!1,te]},useId:function(){var a=Wd.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-Ld(a)-1)).toString(32)+b;var c=ye;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=be++;a=":"+c.idPrefix+
"R"+a;0<b&&(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return we},useEffectEvent:function(){return se},useMemoCache:function(a){for(var b=Array(a),c=0;c<a;c++)b[c]=ud;return b},useHostTransitionStatus:function(){ie();return Ma},useOptimistic:function(a){ie();return[a,ue]},useFormState:function(a,
b,c){ie();var d=ce++,e=Xd;if("function"===typeof a.$$FORM_ACTION){var f=null,g=Yd;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;if(null!==e&&"function"===typeof h){var k=e[1];h.call(a,e[2],e[3])&&(f=void 0!==c?"p"+c:"k"+ja(JSON.stringify([g,null,d]),0),k===f&&(de=d,b=e[0]))}var l=a.bind(null,b);a=function(q){l(q)};"function"===typeof l.$$FORM_ACTION&&(a.$$FORM_ACTION=function(q){q=l.$$FORM_ACTION(q);void 0!==c&&(c+="",q.action=c);var m=q.data;m&&(null===f&&(f=void 0!==c?"p"+c:"k"+ja(JSON.stringify([g,
null,d]),0)),m.append("$ACTION_KEY",f));return q});return[b,a]}var n=a.bind(null,b);return[b,function(q){n(q)}]}},ye=null,Ae={getCacheSignal:function(){throw Error("Not implemented.");},getCacheForType:function(){throw Error("Not implemented.");}},Be=La.ReactCurrentDispatcher,Ce=La.ReactCurrentCache;function De(a){console.error(a);return null}function Fe(){}
function Ge(a,b,c,d,e,f,g,h,k,l,n,q){Na.current=lb;var m=[],w=new Set;b={destination:null,flushScheduled:!1,resumableState:b,renderState:c,rootFormatContext:d,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:w,pingedTasks:m,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===f?De:f,onPostpone:void 0===n?Fe:n,onAllReady:void 0===g?
Fe:g,onShellReady:void 0===h?Fe:h,onShellError:void 0===k?Fe:k,onFatalError:void 0===l?Fe:l,formState:void 0===q?null:q};c=He(b,0,null,d,!1,!1);c.parentFlushed=!0;a=Ie(b,null,a,-1,null,c,w,null,d,yd,null,Jd);m.push(a);return b}var R=null;function Je(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,Ke(a))}
function Le(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,resources:{styles:new Set,stylesheets:new Set},trackedContentKeyPath:null,trackedFallbackNode:null}}
function Ie(a,b,c,d,e,f,g,h,k,l,n,q){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var m={replay:null,node:c,childIndex:d,ping:function(){return Je(a,m)},blockedBoundary:e,blockedSegment:f,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:n,treeContext:q,thenableState:b};g.add(m);return m}
function Me(a,b,c,d,e,f,g,h,k,l,n,q){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var m={replay:c,node:d,childIndex:e,ping:function(){return Je(a,m)},blockedBoundary:f,blockedSegment:null,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:n,treeContext:q,thenableState:b};g.add(m);return m}function He(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}
function V(a,b){a=a.onError(b);if(null!=a&&"string"!==typeof a)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof a+'" instead');return a}function Ne(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function Oe(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error((xd(e)||"Unknown")+'.getChildContext(): key "'+h+'" is not defined in childContextTypes.');e=r({},c,d)}b.legacyContext=e;Y(a,b,null,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,Y(a,b,null,f,-1),b.keyPath=e}
function Pe(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var k=b.blockedSegment;if(null!==k){h=!0;k=k.chunks;for(var l=0;l<f;l++)l===g?k.push("\x3c!--F!--\x3e"):k.push("\x3c!--F--\x3e")}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=Kd(c,1,0),Z(a,b,d,-1),b.treeContext=c):h?Z(a,b,d,-1):Y(a,b,null,d,-1);b.keyPath=f}function Qe(a,b){if(a&&a.defaultProps){b=r({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function Re(a,b,c,d,e,f,g){if("function"===typeof e)if(e.prototype&&e.prototype.isReactComponent){d=zd(e,b.legacyContext);var h=e.contextType;h=new e(f,"object"===typeof h&&null!==h?h._currentValue2:d);Id(h,e,f,d);Oe(a,b,c,h,e)}else{h=zd(e,b.legacyContext);T={};Wd=b;Xd=a;Yd=c;ce=be=0;de=-1;ee=0;fe=d;d=e(f,h);d=le(e,f,d,h);g=0!==be;var k=ce,l=de;"object"===typeof d&&null!==d&&"function"===typeof d.render&&void 0===d.$$typeof?(Id(d,e,f,h),Oe(a,b,c,d,e)):Pe(a,b,c,d,g,k,l)}else if("string"===typeof e)if(d=
b.blockedSegment,null===d)d=f.children,h=b.formatContext,g=b.keyPath,b.formatContext=Ab(h,e,f),b.keyPath=c,Z(a,b,d,-1),b.formatContext=h,b.keyPath=g;else{g=Tb(d.chunks,e,f,a.resumableState,a.renderState,b.formatContext,d.lastPushedText);d.lastPushedText=!1;h=b.formatContext;k=b.keyPath;b.formatContext=Ab(h,e,f);b.keyPath=c;Z(a,b,g,-1);b.formatContext=h;b.keyPath=k;a:{b=d.chunks;a=a.resumableState;switch(e){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;
case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}b.push(Ob(e))}d.lastPushedText=!1}else{switch(e){case rd:case pd:case Kc:case Lc:case Jc:e=b.keyPath;b.keyPath=c;Y(a,b,null,f.children,-1);b.keyPath=e;return;case qd:"hidden"!==f.mode&&(e=b.keyPath,b.keyPath=c,Y(a,b,null,f.children,-1),b.keyPath=e);return;case Rc:e=b.keyPath;b.keyPath=c;Y(a,b,null,f.children,-1);b.keyPath=e;return;case od:throw Error("ReactDOMServer does not yet support scope components.");
case Qc:a:if(null!==b.replay){e=b.keyPath;b.keyPath=c;c=f.children;try{Z(a,b,c,-1)}finally{b.keyPath=e}}else{l=b.keyPath;e=b.blockedBoundary;var n=b.blockedSegment;d=f.fallback;var q=f.children;f=new Set;g=Le(a,f);null!==a.trackedPostpones&&(g.trackedContentKeyPath=c);k=He(a,n.chunks.length,g,b.formatContext,!1,!1);n.children.push(k);n.lastPushedText=!1;var m=He(a,0,null,b.formatContext,!1,!1);m.parentFlushed=!0;b.blockedBoundary=g;b.blockedSegment=m;a.renderState.boundaryResources=g.resources;b.keyPath=
c;try{if(Z(a,b,q,-1),a.renderState.generateStaticMarkup||m.lastPushedText&&m.textEmbedded&&m.chunks.push("\x3c!-- --\x3e"),m.status=1,Se(g,m),0===g.pendingTasks&&0===g.status){g.status=1;break a}}catch(w){m.status=4,g.status=4,"object"===typeof w&&null!==w&&w.$$typeof===vd?(a.onPostpone(w.message),h="POSTPONE"):h=V(a,w),g.errorDigest=h}finally{a.renderState.boundaryResources=e?e.resources:null,b.blockedBoundary=e,b.blockedSegment=n,b.keyPath=l}h=[c[0],"Suspense Fallback",c[2]];l=a.trackedPostpones;
null!==l&&(n=[h[1],h[2],[],null],l.workingMap.set(h,n),5===g.status?l.workingMap.get(c)[4]=n:g.trackedFallbackNode=n);b=Ie(a,null,d,-1,e,k,f,h,b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(b)}return}if("object"===typeof e&&null!==e)switch(e.$$typeof){case Pc:e=e.render;T={};Wd=b;Xd=a;Yd=c;ce=be=0;de=-1;ee=0;fe=d;d=e(f,g);f=le(e,f,d,g);Pe(a,b,c,f,0!==be,ce,de);return;case md:e=e.type;f=Qe(e,f);Re(a,b,c,d,e,f,g);return;case Mc:h=f.children;d=b.keyPath;e=e._context;f=f.value;
g=e._currentValue2;e._currentValue2=f;k=Ad;Ad=f={parent:k,depth:null===k?0:k.depth+1,context:e,parentValue:g,value:f};b.context=f;b.keyPath=c;Y(a,b,null,h,-1);a=Ad;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue2=c===td?a.context._defaultValue:c;a=Ad=a.parent;b.context=a;b.keyPath=d;return;case Nc:f=f.children;f=f(e._currentValue2);e=b.keyPath;b.keyPath=c;Y(a,b,null,f,-1);b.keyPath=e;return;case nd:h=e._init;
e=h(e._payload);f=Qe(e,f);Re(a,b,c,d,e,f,void 0);return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+((null==e?e:typeof e)+"."));}}
function Te(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=He(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,Z(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(Se(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function Y(a,b,c,d,e){if(null!==b.replay&&"number"===typeof b.replay.slots)Te(a,b,b.replay.slots,d,e);else{b.node=d;b.childIndex=e;if("object"===typeof d&&null!==d){switch(d.$$typeof){case Hc:var f=d.type,g=d.key,h=d.props,k=d.ref,l=xd(f),n=null==g?-1===e?0:e:g;g=[b.keyPath,l,n];if(null!==b.replay)a:{var q=b.replay;e=q.nodes;for(d=0;d<e.length;d++){var m=e[d];if(n===m[1]){if(4===m.length){if(null!==l&&l!==m[0])throw Error("Expected the resume to render <"+m[0]+"> in this slot but instead it rendered <"+
l+">. The tree doesn't match so React will fallback to client rendering.");l=m[2];m=m[3];n=b.node;b.replay={nodes:l,slots:m,pendingTasks:1};try{Re(a,b,g,c,f,h,k);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(p){if("object"===typeof p&&null!==p&&(p===Pd||"function"===typeof p.then))throw b.node===n&&(b.replay=q),p;b.replay.pendingTasks--;
Ue(a,b.blockedBoundary,p,l,m)}b.replay=q}else{if(f!==Qc)throw Error("Expected the resume to render <Suspense> in this slot but instead it rendered <"+(xd(f)||"Unknown")+">. The tree doesn't match so React will fallback to client rendering.");b:{c=void 0;f=m[5];k=m[2];q=m[3];l=null===m[4]?[]:m[4][2];m=null===m[4]?null:m[4][3];n=b.keyPath;var w=b.replay,C=b.blockedBoundary,O=h.children;h=h.fallback;var v=new Set,t=Le(a,v);t.parentFlushed=!0;t.rootSegmentID=f;b.blockedBoundary=t;b.replay={nodes:k,slots:q,
pendingTasks:1};a.renderState.boundaryResources=t.resources;try{Z(a,b,O,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--;if(0===t.pendingTasks&&0===t.status){t.status=1;a.completedBoundaries.push(t);break b}}catch(p){t.status=4,"object"===typeof p&&null!==p&&p.$$typeof===vd?(a.onPostpone(p.message),c="POSTPONE"):c=V(a,p),
t.errorDigest=c,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(t)}finally{a.renderState.boundaryResources=C?C.resources:null,b.blockedBoundary=C,b.replay=w,b.keyPath=n}h=Me(a,null,{nodes:l,slots:m,pendingTasks:0},h,-1,C,v,[g[0],"Suspense Fallback",g[2]],b.formatContext,b.legacyContext,b.context,b.treeContext);a.pingedTasks.push(h)}}e.splice(d,1);break a}}}else Re(a,b,g,c,f,h,k);return;case Ic:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");
case nd:h=d._init;d=h(d._payload);Y(a,b,null,d,e);return}if(Ka(d)){Ve(a,b,d,e);return}null===d||"object"!==typeof d?h=null:(h=wd&&d[wd]||d["@@iterator"],h="function"===typeof h?h:null);if(h&&(h=h.call(d))){d=h.next();if(!d.done){g=[];do g.push(d.value),d=h.next();while(!d.done);Ve(a,b,g,e)}return}if("function"===typeof d.then)return Y(a,b,null,ve(d),e);if(d.$$typeof===Nc||d.$$typeof===Oc)return Y(a,b,null,d._currentValue2,e);e=Object.prototype.toString.call(d);throw Error("Objects are not valid as a React child (found: "+
("[object Object]"===e?"object with keys {"+Object.keys(d).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof d?(e=b.blockedSegment,null!==e&&(e.lastPushedText=Gc(e.chunks,d,a.renderState,e.lastPushedText))):"number"===typeof d&&(e=b.blockedSegment,null!==e&&(e.lastPushedText=Gc(e.chunks,""+d,a.renderState,e.lastPushedText)))}}
function Ve(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var k=g[h];if(k[1]===d){d=k[2];k=k[3];b.replay={nodes:d,slots:k,pendingTasks:1};try{Ve(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(n){if("object"===typeof n&&
null!==n&&(n===Pd||"function"===typeof n.then))throw n;b.replay.pendingTasks--;Ue(a,b.blockedBoundary,n,d,k)}b.replay=f;g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(k=0;k<g;k++){d=c[k];b.treeContext=Kd(f,g,k);var l=h[k];"number"===typeof l?(Te(a,b,l,d,k),delete h[k]):Z(a,b,d,k)}b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)k=c[h],b.treeContext=Kd(f,g,h),Z(a,b,k,h);b.treeContext=f;b.keyPath=e}
function We(a,b,c,d){d.status=5;var e=c.keyPath,f=c.blockedBoundary;if(null===f)d.id=a.nextSegmentId++,b.rootSlots=d.id,null!==a.completedRootSegment&&(a.completedRootSegment.status=5);else{if(null!==f&&0===f.status){f.status=5;f.rootSegmentID=a.nextSegmentId++;var g=f.trackedContentKeyPath;if(null===g)throw Error("It should not be possible to postpone at the root. This is a bug in React.");var h=f.trackedFallbackNode,k=[];if(g===e&&-1===c.childIndex){-1===d.id&&(d.id=d.parentFlushed?f.rootSegmentID:
a.nextSegmentId++);d=[g[1],g[2],k,d.id,h,f.rootSegmentID];b.workingMap.set(g,d);Xe(d,g[0],b);return}var l=b.workingMap.get(g);void 0===l?(l=[g[1],g[2],k,null,h,f.rootSegmentID],b.workingMap.set(g,l),Xe(l,g[0],b)):(g=l,g[4]=h,g[5]=f.rootSegmentID)}-1===d.id&&(d.id=d.parentFlushed&&null!==f?f.rootSegmentID:a.nextSegmentId++);if(-1===c.childIndex)null===e?b.rootSlots=d.id:(c=b.workingMap.get(e),void 0===c?(c=[e[1],e[2],[],d.id],Xe(c,e[0],b)):c[3]=d.id);else{if(null===e)if(a=b.rootSlots,null===a)a=b.rootSlots=
{};else{if("number"===typeof a)throw Error("It should not be possible to postpone both at the root of an element as well as a slot below. This is a bug in React.");}else if(f=b.workingMap,g=f.get(e),void 0===g)a={},g=[e[1],e[2],[],a],f.set(e,g),Xe(g,e[0],b);else if(a=g[3],null===a)a=g[3]={};else if("number"===typeof a)throw Error("It should not be possible to postpone both at the root of an element as well as a slot below. This is a bug in React.");a[c.childIndex]=d.id}}}
function Z(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,k=b.treeContext,l=b.blockedSegment;if(null===l)try{return Y(a,b,null,c,d)}catch(m){if(me(),d=m===Pd?Td():m,"object"===typeof d&&null!==d&&"function"===typeof d.then){c=d;d=ne();a=Me(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;Gd(g);return}}else{var n=
l.children.length,q=l.chunks.length;try{return Y(a,b,null,c,d)}catch(m){if(me(),l.children.length=n,l.chunks.length=q,d=m===Pd?Td():m,"object"===typeof d&&null!==d){if("function"===typeof d.then){c=d;d=ne();l=b.blockedSegment;n=He(a,l.chunks.length,null,b.formatContext,l.lastPushedText,!0);l.children.push(n);l.lastPushedText=!1;a=Ie(a,d,b.node,b.childIndex,b.blockedBoundary,n,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext).ping;c.then(a,a);b.formatContext=e;b.legacyContext=
f;b.context=g;b.keyPath=h;b.treeContext=k;Gd(g);return}if(null!==a.trackedPostpones&&d.$$typeof===vd&&null!==b.blockedBoundary){c=a.trackedPostpones;a.onPostpone(d.message);d=b.blockedSegment;l=He(a,d.chunks.length,null,b.formatContext,d.lastPushedText,!0);d.children.push(l);d.lastPushedText=!1;We(a,c,b,l);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;Gd(g);return}}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;Gd(g);throw d;}
function Ue(a,b,c,d,e){if("object"===typeof c&&null!==c&&c.$$typeof===vd){a.onPostpone(c.message);var f="POSTPONE"}else f=V(a,c);Ye(a,b,d,e,c,f)}function Ze(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,$e(this,b,a))}
function Ye(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)Ye(a,b,h[2],h[3],e,f);else{h=h[5];var k=a,l=f,n=Le(k,new Set);n.parentFlushed=!0;n.rootSegmentID=h;n.status=4;n.errorDigest=l;n.parentFlushed&&k.clientRenderedBoundaries.push(n)}}c.length=0;if(null!==d){if(null===b)throw Error("We should not have any resumable nodes in the shell. This is a bug in React.");4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var q in d)delete d[q]}}
function af(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(1!==b.status&&2!==b.status){a=a.replay;if(null===a){V(b,c);Ne(b,c);return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&(d=V(b,c),Ye(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&bf(b)}}else d.pendingTasks--,4!==d.status&&(d.status=4,d.errorDigest=V(b,c),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return af(f,
b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&cf(b)}
function df(a,b){try{var c=a.renderState,d=c.onHeaders;if(d){var e=c.headers;if(e){c.headers=null;var f=e.preconnects;e.fontPreloads&&(f&&(f+=", "),f+=e.fontPreloads);e.highImagePreloads&&(f&&(f+=", "),f+=e.highImagePreloads);if(!b){var g=c.styles.values(),h=g.next();b:for(;0<e.remainingCapacity&&!h.done;h=g.next())for(var k=h.value.sheets.values(),l=k.next();0<e.remainingCapacity&&!l.done;l=k.next()){var n=l.value,q=n.props,m=q.href,w=n.props,C=Ub(w.href,"style",{crossOrigin:w.crossOrigin,integrity:w.integrity,
nonce:w.nonce,type:w.type,fetchPriority:w.fetchPriority,referrerPolicy:w.referrerPolicy,media:w.media});if(2<=(e.remainingCapacity-=C.length))c.resets.style[m]=B,f&&(f+=", "),f+=C,c.resets.style[m]="string"===typeof q.crossOrigin||"string"===typeof q.integrity?[q.crossOrigin,q.integrity]:B;else break b}}f?d({Link:f}):d({})}}}catch(O){V(a,O)}}function bf(a){null===a.trackedPostpones&&df(a,!0);a.onShellError=Fe;a=a.onShellReady;a()}
function cf(a){df(a,null===a.trackedPostpones?!0:null===a.completedRootSegment||5!==a.completedRootSegment.status);a=a.onAllReady;a()}function Se(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary&&-1===b.children[0].id){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&Se(a,c)}else a.completedSegments.push(b)}
function $e(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&bf(a)}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&Se(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(Ze,a),b.fallbackAbortableTasks.clear())):
null!==c&&c.parentFlushed&&1===c.status&&(Se(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&cf(a)}
function Ke(a){if(2!==a.status){var b=Ad,c=Be.current;Be.current=ze;var d=Ce.current;Ce.current=Ae;var e=R;R=a;var f=ye;ye=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var k=g[h],l=a,n=k.blockedBoundary;l.renderState.boundaryResources=n?n.resources:null;var q=k.blockedSegment;if(null===q){var m=l;if(0!==k.replay.pendingTasks){Gd(k.context);try{var w=k.thenableState;k.thenableState=null;Y(m,k,w,k.node,k.childIndex);if(1===k.replay.pendingTasks&&0<k.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
k.replay.pendingTasks--;k.abortSet.delete(k);$e(m,k.blockedBoundary,null)}catch(y){me();var C=y===Pd?Td():y;if("object"===typeof C&&null!==C&&"function"===typeof C.then){var O=k.ping;C.then(O,O);k.thenableState=ne()}else k.replay.pendingTasks--,k.abortSet.delete(k),Ue(m,k.blockedBoundary,C,k.replay.nodes,k.replay.slots),m.pendingRootTasks--,0===m.pendingRootTasks&&bf(m),m.allPendingTasks--,0===m.allPendingTasks&&cf(m)}finally{m.renderState.boundaryResources=null}}}else a:{m=void 0;var v=q;if(0===
v.status){Gd(k.context);var t=v.children.length,p=v.chunks.length;try{var G=k.thenableState;k.thenableState=null;Y(l,k,G,k.node,k.childIndex);l.renderState.generateStaticMarkup||v.lastPushedText&&v.textEmbedded&&v.chunks.push("\x3c!-- --\x3e");k.abortSet.delete(k);v.status=1;$e(l,k.blockedBoundary,v)}catch(y){me();v.children.length=t;v.chunks.length=p;var u=y===Pd?Td():y;if("object"===typeof u&&null!==u){if("function"===typeof u.then){var x=k.ping;u.then(x,x);k.thenableState=ne();break a}if(null!==
l.trackedPostpones&&u.$$typeof===vd){var E=l.trackedPostpones;k.abortSet.delete(k);l.onPostpone(u.message);We(l,E,k,v);$e(l,k.blockedBoundary,v);break a}}k.abortSet.delete(k);v.status=4;var D=k.blockedBoundary;"object"===typeof u&&null!==u&&u.$$typeof===vd?(l.onPostpone(u.message),m="POSTPONE"):m=V(l,u);null===D?Ne(l,u):(D.pendingTasks--,4!==D.status&&(D.status=4,D.errorDigest=m,D.parentFlushed&&l.clientRenderedBoundaries.push(D)));l.allPendingTasks--;0===l.allPendingTasks&&cf(l)}finally{l.renderState.boundaryResources=
null}}}}g.splice(0,h);null!==a.destination&&ef(a,a.destination)}catch(y){V(a,y),Ne(a,y)}finally{ye=f,Be.current=c,Ce.current=d,c===ze&&Gd(b),R=e}}}
function ff(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:var d=c.id;c.lastPushedText=!1;c.textEmbedded=!1;a=a.renderState;b.push('<template id="');b.push(a.placeholderPrefix);a=d.toString(16);b.push(a);return b.push('"></template>');case 1:c.status=2;var e=!0;d=c.chunks;var f=0;c=c.children;for(var g=0;g<c.length;g++){for(e=c[g];f<e.index;f++)b.push(d[f]);e=gf(a,b,e)}for(;f<d.length-1;f++)b.push(d[f]);f<d.length&&(e=b.push(d[f]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function gf(a,b,c){var d=c.boundary;if(null===d)return ff(a,b,c);d.parentFlushed=!0;if(4===d.status)return a.renderState.generateStaticMarkup||(d=d.errorDigest,b.push("\x3c!--$!--\x3e"),b.push("<template"),d&&(b.push(' data-dgst="'),d=A(d),b.push(d),b.push('"')),b.push("></template>")),ff(a,b,c),a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e"),a;if(1!==d.status)return 0===d.status&&(d.rootSegmentID=a.nextSegmentId++),0<d.completedSegments.length&&a.partialBoundaries.push(d),Xb(b,a.renderState,
d.rootSegmentID),ff(a,b,c),b.push("\x3c!--/$--\x3e");if(d.byteSize>a.progressiveChunkSize)return d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),Xb(b,a.renderState,d.rootSegmentID),ff(a,b,c),b.push("\x3c!--/$--\x3e");c=d.resources;var e=a.renderState.boundaryResources;e&&(c.styles.forEach(Dc,e),c.stylesheets.forEach(Ec,e));a.renderState.generateStaticMarkup||b.push("\x3c!--$--\x3e");c=d.completedSegments;if(1!==c.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");
gf(a,b,c[0]);a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e");return a}function hf(a,b,c){gc(b,a.renderState,c.parentFormatContext,c.id);gf(a,b,c);return hc(b,c.parentFormatContext)}
function jf(a,b,c){a.renderState.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)kf(a,b,c,d[e]);d.length=0;qc(b,c.resources,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.resources;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(b.push(a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
0===(d.instructions&8)?(d.instructions|=8,b.push('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
b.push('$RR("'):0===(d.instructions&2)?(d.instructions|=2,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("')):
b.push('$RC("')):f?b.push('<template data-rri="" data-bid="'):b.push('<template data-rci="" data-bid="');d=e.toString(16);b.push(a.boundaryPrefix);b.push(d);g?b.push('","'):b.push('" data-sid="');b.push(a.segmentPrefix);b.push(d);f?g?(b.push('",'),wc(b,c)):(b.push('" data-sty="'),xc(b,c)):g&&b.push('"');d=g?b.push(")\x3c/script>"):b.push('"></template>');return Wb(b,a)&&d}
function kf(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return hf(a,b,d)}if(e===c.rootSegmentID)return hf(a,b,d);hf(a,b,d);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(b.push(a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,b.push('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("')):
b.push('$RS("')):b.push('<template data-rsi="" data-sid="');b.push(a.segmentPrefix);e=e.toString(16);b.push(e);d?b.push('","'):b.push('" data-pid="');b.push(a.placeholderPrefix);b.push(e);b=d?b.push('")\x3c/script>'):b.push('"></template>');return b}
function ef(a,b){try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,k=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(k))}var l=e.htmlChunks,n=e.headChunks;f=0;if(l){for(f=0;f<l.length;f++)b.push(l[f]);if(n)for(f=0;f<n.length;f++)b.push(n[f]);else{var q=N("head");b.push(q);
b.push(">")}}else if(n)for(f=0;f<n.length;f++)b.push(n[f]);var m=e.charsetChunks;for(f=0;f<m.length;f++)b.push(m[f]);m.length=0;e.preconnects.forEach(P,b);e.preconnects.clear();var w=e.preconnectChunks;for(f=0;f<w.length;f++)b.push(w[f]);w.length=0;e.fontPreloads.forEach(P,b);e.fontPreloads.clear();e.highImagePreloads.forEach(P,b);e.highImagePreloads.clear();e.styles.forEach(tc,b);var C=e.importMapChunks;for(f=0;f<C.length;f++)b.push(C[f]);C.length=0;e.bootstrapScripts.forEach(P,b);e.scripts.forEach(P,
b);e.scripts.clear();e.bulkPreloads.forEach(P,b);e.bulkPreloads.clear();var O=e.preloadChunks;for(f=0;f<O.length;f++)b.push(O[f]);O.length=0;var v=e.hoistableChunks;for(f=0;f<v.length;f++)b.push(v[f]);v.length=0;if(l&&null===n){var t=Ob("head");b.push(t)}gf(a,b,d);a.completedRootSegment=null;Wb(b,a.renderState)}else return;var p=a.renderState;d=0;p.preconnects.forEach(P,b);p.preconnects.clear();var G=p.preconnectChunks;for(d=0;d<G.length;d++)b.push(G[d]);G.length=0;p.fontPreloads.forEach(P,b);p.fontPreloads.clear();
p.highImagePreloads.forEach(P,b);p.highImagePreloads.clear();p.styles.forEach(vc,b);p.scripts.forEach(P,b);p.scripts.clear();p.bulkPreloads.forEach(P,b);p.bulkPreloads.clear();var u=p.preloadChunks;for(d=0;d<u.length;d++)b.push(u[d]);u.length=0;var x=p.hoistableChunks;for(d=0;d<x.length;d++)b.push(x[d]);x.length=0;var E=a.clientRenderedBoundaries;for(c=0;c<E.length;c++){var D=E[c];p=b;var y=a.resumableState,Pa=a.renderState,qb=D.rootSegmentID,Ba=D.errorDigest,ka=D.errorMessage,da=D.errorComponentStack,
W=0===y.streamingFormat;W?(p.push(Pa.startInlineScript),0===(y.instructions&4)?(y.instructions|=4,p.push('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("')):p.push('$RX("')):p.push('<template data-rxi="" data-bid="');p.push(Pa.boundaryPrefix);var Qa=qb.toString(16);p.push(Qa);W&&p.push('"');if(Ba||ka||da)if(W){p.push(",");var Ra=jc(Ba||"");p.push(Ra)}else{p.push('" data-dgst="');
var Sa=A(Ba||"");p.push(Sa)}if(ka||da)if(W){p.push(",");var la=jc(ka||"");p.push(la)}else{p.push('" data-msg="');var Q=A(ka||"");p.push(Q)}if(da)if(W){p.push(",");var rb=jc(da);p.push(rb)}else{p.push('" data-stck="');var ma=A(da);p.push(ma)}if(W?!p.push(")\x3c/script>"):!p.push('"></template>')){a.destination=null;c++;E.splice(0,c);return}}E.splice(0,c);var na=a.completedBoundaries;for(c=0;c<na.length;c++)if(!jf(a,b,na[c])){a.destination=null;c++;na.splice(0,c);return}na.splice(0,c);var aa=a.partialBoundaries;
for(c=0;c<aa.length;c++){var oa=aa[c];a:{E=a;D=b;E.renderState.boundaryResources=oa.resources;var pa=oa.completedSegments;for(y=0;y<pa.length;y++)if(!kf(E,D,oa,pa[y])){y++;pa.splice(0,y);var Ta=!1;break a}pa.splice(0,y);Ta=qc(D,oa.resources,E.renderState)}if(!Ta){a.destination=null;c++;aa.splice(0,c);return}}aa.splice(0,c);var qa=a.completedBoundaries;for(c=0;c<qa.length;c++)if(!jf(a,b,qa[c])){a.destination=null;c++;qa.splice(0,c);return}qa.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&
0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length&&(a.flushScheduled=!1,null===a.trackedPostpones&&(c=a.resumableState,c.hasBody&&(aa=Ob("body"),b.push(aa)),c.hasHtml&&(c=Ob("html"),b.push(c))),b.push(null),a.destination=null)}}function lf(a){a.flushScheduled=null!==a.destination;Ke(a);null===a.trackedPostpones&&df(a,0===a.pendingRootTasks)}
function Ac(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){a.flushScheduled=!0;var b=a.destination;b?ef(a,b):a.flushScheduled=!1}}function mf(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{ef(a,b)}catch(c){V(a,c),Ne(a,c)}}}
function nf(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return af(e,a,d)});c.clear()}null!==a.destination&&ef(a,a.destination)}catch(e){V(a,e),Ne(a,e)}}function Xe(a,b,c){if(null===b)c.rootNodes.push(a);else{var d=c.workingMap,e=d.get(b);void 0===e&&(e=[b[1],b[2],[],null],d.set(b,e),Xe(e,b[0],c));e[2].push(a)}}function of(){}
function pf(a,b,c,d){var e=!1,f=null,g="",h=!1;b=ob(b?b.identifierPrefix:void 0,void 0);a=Ge(a,b,Fc(b,c),pb(),Infinity,of,void 0,function(){h=!0},void 0,void 0,void 0);lf(a);nf(a,d);mf(a,{push:function(k){null!==k&&(g+=k);return!0},destroy:function(k){e=!0;f=k}});if(e&&f!==d)throw f;if(!h)throw Error("A component suspended while responding to synchronous input. This will cause the UI to be replaced with a loading indicator. To fix, updates that suspend should be wrapped with startTransition.");return g}
function qf(a,b){a.prototype=Object.create(b.prototype);a.prototype.constructor=a;a.__proto__=b}var rf=function(a){function b(){var d=a.call(this,{})||this;d.request=null;d.startedFlowing=!1;return d}qf(b,a);var c=b.prototype;c._destroy=function(d,e){nf(this.request);e(d)};c._read=function(){this.startedFlowing&&mf(this.request,this)};return b}(ia.Readable);function sf(){}
function tf(a,b){var c=new rf;b=ob(b?b.identifierPrefix:void 0,void 0);var d=Ge(a,b,Fc(b,!1),pb(),Infinity,sf,function(){c.startedFlowing=!0;mf(d,c)},void 0,void 0,void 0);c.request=d;lf(d);return c}exports.renderToNodeStream=function(a,b){return tf(a,b)};exports.renderToStaticMarkup=function(a,b){return pf(a,b,!0,'The server used "renderToStaticMarkup" which does not support Suspense. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.renderToStaticNodeStream=function(a,b){return tf(a,b)};exports.renderToString=function(a,b){return pf(a,b,!1,'The server used "renderToString" which does not support Suspense. If you intended for this Suspense boundary to render the fallback content on the server consider throwing an Error somewhere within the Suspense boundary. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.version="18.3.0-experimental-2c338b16f-20231116";

//# sourceMappingURL=react-dom-server-legacy.node.production.min.js.map
