{"version": 3, "sources": ["../../../../../../../src/build/webpack/loaders/css-loader/src/plugins/postcss-import-parser.ts"], "names": ["valueParser", "normalizeUrl", "resolveRequests", "isUrlRequestable", "requestify", "WEBPACK_IGNORE_COMMENT_REGEXP", "parseNode", "atRule", "key", "parent", "type", "raws", "after<PERSON>ame", "trim", "length", "lastCommentIndex", "lastIndexOf", "matched", "slice", "match", "prevNode", "prev", "text", "nodes", "error", "Error", "node", "paramsNodes", "toString", "isStringValue", "url", "value", "toLowerCase", "stringify", "isRequestable", "prefix", "queryParts", "split", "pop", "join", "mediaNodes", "media", "plugin", "options", "postcssPlugin", "prepare", "result", "parsedAtRules", "AtRule", "import", "parsedAtRule", "warn", "message", "push", "OnceExit", "resolvedAtRules", "Promise", "all", "map", "filter", "<PERSON><PERSON><PERSON>", "request", "rootContext", "resolver", "context", "resolvedUrl", "Set", "resourcePath", "remove", "urlToNameMap", "Map", "index", "resolvedAtRule", "api", "newUrl", "importName", "get", "size", "set", "imports", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "postcss"], "mappings": "AAAA,OAAOA,iBAAiB,0CAAyC;AAEjE,SACEC,YAAY,EACZC,eAAe,EACfC,gBAAgB,EAChBC,UAAU,EAEVC,AADA,kEAAkE;AAClEA,6BAA6B,QACxB,WAAU;AAEjB,SAASC,UAAUC,MAAW,EAAEC,GAAQ;IACtC,iCAAiC;IACjC,IAAID,OAAOE,MAAM,CAACC,IAAI,KAAK,QAAQ;QACjC;IACF;IAEA,IACEH,OAAOI,IAAI,IACXJ,OAAOI,IAAI,CAACC,SAAS,IACrBL,OAAOI,IAAI,CAACC,SAAS,CAACC,IAAI,GAAGC,MAAM,GAAG,GACtC;QACA,MAAMC,mBAAmBR,OAAOI,IAAI,CAACC,SAAS,CAACI,WAAW,CAAC;QAC3D,MAAMC,UAAUV,OAAOI,IAAI,CAACC,SAAS,CAClCM,KAAK,CAACH,kBACNI,KAAK,CAACd;QAET,IAAIY,WAAWA,OAAO,CAAC,EAAE,KAAK,QAAQ;YACpC;QACF;IACF;IAEA,MAAMG,WAAWb,OAAOc,IAAI;IAE5B,IAAID,YAAYA,SAASV,IAAI,KAAK,WAAW;QAC3C,MAAMO,UAAUG,SAASE,IAAI,CAACH,KAAK,CAACd;QAEpC,IAAIY,WAAWA,OAAO,CAAC,EAAE,KAAK,QAAQ;YACpC;QACF;IACF;IAEA,0DAA0D;IAC1D,IAAIV,OAAOgB,KAAK,EAAE;QAChB,MAAMC,QAAa,IAAIC,MACrB;QAGFD,MAAME,IAAI,GAAGnB;QAEb,MAAMiB;IACR;IAEA,MAAM,EAAED,OAAOI,WAAW,EAAE,GAAG3B,YAAYO,MAAM,CAACC,IAAI;IAEtD,yBAAyB;IACzB,oCAAoC;IACpC,IACEmB,YAAYb,MAAM,KAAK,KACtBa,WAAW,CAAC,EAAE,CAACjB,IAAI,KAAK,YAAYiB,WAAW,CAAC,EAAE,CAACjB,IAAI,KAAK,YAC7D;QACA,MAAMc,QAAa,IAAIC,MAAM,CAAC,uBAAuB,EAAElB,OAAOqB,QAAQ,GAAG,CAAC,CAAC;QAE3EJ,MAAME,IAAI,GAAGnB;QAEb,MAAMiB;IACR;IAEA,IAAIK;IACJ,IAAIC;IAEJ,IAAIH,WAAW,CAAC,EAAE,CAACjB,IAAI,KAAK,UAAU;QACpCmB,gBAAgB;QAChBC,MAAMH,WAAW,CAAC,EAAE,CAACI,KAAK;IAC5B,OAAO;QACL,gDAAgD;QAChD,IAAIJ,WAAW,CAAC,EAAE,CAACI,KAAK,CAACC,WAAW,OAAO,OAAO;YAChD,MAAMR,QAAa,IAAIC,MACrB,CAAC,uBAAuB,EAAElB,OAAOqB,QAAQ,GAAG,CAAC,CAAC;YAGhDJ,MAAME,IAAI,GAAGnB;YAEb,MAAMiB;QACR;QAEAK,gBACEF,WAAW,CAAC,EAAE,CAACJ,KAAK,CAACT,MAAM,KAAK,KAChCa,WAAW,CAAC,EAAE,CAACJ,KAAK,CAAC,EAAE,CAACb,IAAI,KAAK;QACnCoB,MAAMD,gBACFF,WAAW,CAAC,EAAE,CAACJ,KAAK,CAAC,EAAE,CAACQ,KAAK,GAC7B/B,YAAYiC,SAAS,CAACN,WAAW,CAAC,EAAE,CAACJ,KAAK;IAChD;IAEAO,MAAM7B,aAAa6B,KAAKD;IAExB,MAAMK,gBAAgB/B,iBAAiB2B;IACvC,IAAIK;IAEJ,IAAID,eAAe;QACjB,MAAME,aAAaN,IAAIO,KAAK,CAAC;QAE7B,IAAID,WAAWtB,MAAM,GAAG,GAAG;YACzBgB,MAAMM,WAAWE,GAAG;YACpBH,SAASC,WAAWG,IAAI,CAAC;QAC3B;IACF;IAEA,gDAAgD;IAChD,IAAIT,IAAIjB,IAAI,GAAGC,MAAM,KAAK,GAAG;QAC3B,MAAMU,QAAa,IAAIC,MAAM,CAAC,uBAAuB,EAAElB,OAAOqB,QAAQ,GAAG,CAAC,CAAC;QAE3EJ,MAAME,IAAI,GAAGnB;QAEb,MAAMiB;IACR;IAEA,MAAMgB,aAAab,YAAYT,KAAK,CAAC;IACrC,IAAIuB;IAEJ,IAAID,WAAW1B,MAAM,GAAG,GAAG;QACzB2B,QAAQzC,YAAYiC,SAAS,CAACO,YAAY3B,IAAI,GAAGmB,WAAW;IAC9D;IAEA,6CAA6C;IAC7C,OAAO;QAAEzB;QAAQ4B;QAAQL;QAAKW;QAAOP;IAAc;AACrD;AAEA,MAAMQ,SAAS,CAACC,UAAe,CAAC,CAAC;IAC/B,OAAO;QACLC,eAAe;QACfC,SAAQC,MAAW;YACjB,MAAMC,gBAAuB,EAAE;YAE/B,OAAO;gBACLC,QAAQ;oBACNC,QAAO1C,MAAW;wBAChB,IAAI2C;wBAEJ,IAAI;4BACF,qDAAqD;4BACrDA,eAAe5C,UAAUC,QAAQ,UAAUuC;wBAC7C,EAAE,OAAOtB,OAAY;4BACnBsB,OAAOK,IAAI,CAAC3B,MAAM4B,OAAO,EAAE;gCAAE1B,MAAMF,MAAME,IAAI;4BAAC;wBAChD;wBAEA,IAAI,CAACwB,cAAc;4BACjB;wBACF;wBAEAH,cAAcM,IAAI,CAACH;oBACrB;gBACF;gBACA,MAAMI;oBACJ,IAAIP,cAAcjC,MAAM,KAAK,GAAG;wBAC9B;oBACF;oBAEA,MAAMyC,kBAAkB,MAAMC,QAAQC,GAAG,CACvCV,cAAcW,GAAG,CAAC,OAAOR;wBACvB,MAAM,EAAE3C,MAAM,EAAE2B,aAAa,EAAEC,MAAM,EAAEL,GAAG,EAAEW,KAAK,EAAE,GAAGS;wBAEtD,IAAIP,QAAQgB,MAAM,EAAE;4BAClB,MAAMC,WAAW,MAAMjB,QAAQgB,MAAM,CAAC7B,KAAKW;4BAE3C,IAAI,CAACmB,UAAU;gCACb;4BACF;wBACF;wBAEA,IAAI1B,eAAe;4BACjB,MAAM2B,UAAUzD,WAAW0B,KAAKa,QAAQmB,WAAW;4BAEnD,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAE,GAAGrB;4BAC9B,MAAMsB,cAAc,MAAM/D,gBAAgB6D,UAAUC,SAAS;mCACxD,IAAIE,IAAI;oCAACL;oCAAS/B;iCAAI;6BAC1B;4BAED,IAAI,CAACmC,aAAa;gCAChB;4BACF;4BAEA,IAAIA,gBAAgBtB,QAAQwB,YAAY,EAAE;gCACxC5D,OAAO6D,MAAM;gCAEb;4BACF;4BAEA7D,OAAO6D,MAAM;4BAEb,6CAA6C;4BAC7C,OAAO;gCAAEtC,KAAKmC;gCAAaxB;gCAAON;gCAAQD;4BAAc;wBAC1D;wBAEA3B,OAAO6D,MAAM;wBAEb,6CAA6C;wBAC7C,OAAO;4BAAEtC;4BAAKW;4BAAON;4BAAQD;wBAAc;oBAC7C;oBAGF,MAAMmC,eAAe,IAAIC;oBAEzB,IAAK,IAAIC,QAAQ,GAAGA,SAAShB,gBAAgBzC,MAAM,GAAG,GAAGyD,QAAS;wBAChE,MAAMC,iBAAiBjB,eAAe,CAACgB,MAAM;wBAE7C,IAAI,CAACC,gBAAgB;4BAEnB;wBACF;wBAEA,MAAM,EAAE1C,GAAG,EAAEI,aAAa,EAAEO,KAAK,EAAE,GAAG+B;wBAEtC,IAAI,CAACtC,eAAe;4BAClBS,QAAQ8B,GAAG,CAACpB,IAAI,CAAC;gCAAEvB;gCAAKW;gCAAO8B;4BAAM;4BAGrC;wBACF;wBAEA,MAAM,EAAEpC,MAAM,EAAE,GAAGqC;wBACnB,MAAME,SAASvC,SAAS,CAAC,EAAEA,OAAO,CAAC,EAAEL,IAAI,CAAC,GAAGA;wBAC7C,IAAI6C,aAAaN,aAAaO,GAAG,CAACF;wBAElC,IAAI,CAACC,YAAY;4BACfA,aAAa,CAAC,6BAA6B,EAAEN,aAAaQ,IAAI,CAAC,GAAG,CAAC;4BACnER,aAAaS,GAAG,CAACJ,QAAQC;4BAEzBhC,QAAQoC,OAAO,CAAC1B,IAAI,CAAC;gCACnB3C,MAAM;gCACNiE;gCACA7C,KAAKa,QAAQqC,UAAU,CAACN;gCACxBH;4BACF;wBACF;wBAEA5B,QAAQ8B,GAAG,CAACpB,IAAI,CAAC;4BAAEsB;4BAAYlC;4BAAO8B;wBAAM;oBAC9C;gBACF;YACF;QACF;IACF;AACF;AAEA7B,OAAOuC,OAAO,GAAG;AAEjB,eAAevC,OAAM"}