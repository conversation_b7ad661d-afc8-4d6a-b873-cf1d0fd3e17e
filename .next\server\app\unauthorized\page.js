(()=>{var e={};e.id=847,e.ids=[847],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6361:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>l});var s=r(6625),n=r(5345),a=r(4666),o=r.n(a),i=r(8889),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let l=["",{children:["unauthorized",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,584)),"C:\\Projects\\augment_code_v0_compare\\app\\unauthorized\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6013)),"C:\\Projects\\augment_code_v0_compare\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,8363)),"C:\\Projects\\augment_code_v0_compare\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,1737)),"C:\\Projects\\augment_code_v0_compare\\app\\not-found.tsx"]}],d=["C:\\Projects\\augment_code_v0_compare\\app\\unauthorized\\page.tsx"],u="/unauthorized/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/unauthorized/page",pathname:"/unauthorized",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},9782:(e,t,r)=>{Promise.resolve().then(r.bind(r,948)),Promise.resolve().then(r.t.bind(r,8116,23))},584:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(4699),n=r(2804),a=r.n(n),o=r(2600),i=r(3103),c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),d=(e,t)=>{let r=(0,i.forwardRef)(({color:r="currentColor",size:s=24,strokeWidth:n=2,absoluteStrokeWidth:a,className:o="",children:d,...u},p)=>(0,i.createElement)("svg",{ref:p,...c,width:s,height:s,stroke:r,strokeWidth:a?24*Number(n)/Number(s):n,className:["lucide",`lucide-${l(e)}`,o].join(" "),...u},[...t.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(d)?d:[d]]));return r.displayName=`${e}`,r},u=d("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]),p=d("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]),m=d("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);function x(){return s.jsx(o.A,{children:s.jsx("div",{className:"min-h-[60vh] flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,s.jsxs)("div",{className:"mb-8",children:[s.jsx("div",{className:"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(u,{size:32,className:"text-red-600"})}),s.jsx("h1",{className:"text-3xl font-bold text-primary-900 mb-4",children:"Unauthorized Access"}),s.jsx("p",{className:"text-primary-600 leading-relaxed",children:"You don't have permission to access this page. Please sign in with an authorized account to continue."})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(a(),{href:"/signin",className:"btn-primary inline-flex items-center space-x-2",children:[s.jsx(p,{size:16}),s.jsx("span",{children:"Sign In"})]}),(0,s.jsxs)(a(),{href:"/",className:"btn-secondary inline-flex items-center space-x-2 ml-4",children:[s.jsx(m,{size:16}),s.jsx("span",{children:"Go Home"})]})]})]})})})}},2600:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(8164);let n=(0,s.createProxy)(String.raw`C:\Projects\augment_code_v0_compare\components\Layout.tsx`),{__esModule:a,$$typeof:o}=n;n.default;let i=(0,s.createProxy)(String.raw`C:\Projects\augment_code_v0_compare\components\Layout.tsx#Layout`)},6310:(e,t,r)=>{let{createProxy:s}=r(8164);e.exports=s("C:\\Projects\\augment_code_v0_compare\\node_modules\\.pnpm\\next@14.0.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\client\\link.js")},2804:(e,t,r)=>{e.exports=r(6310)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[545,653],()=>r(6361));module.exports=s})();