"use strict";(()=>{var e={};e.id=847,e.ids=[847],e.modules={7849:e=>{e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6361:(e,t,r)=>{r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var n=r(6625),s=r(5345),a=r(4666),i=r.n(a),o=r(8889),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["unauthorized",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4369)),"C:\\Projects\\augment_code_v0_compare\\app\\unauthorized\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6013)),"C:\\Projects\\augment_code_v0_compare\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,8363)),"C:\\Projects\\augment_code_v0_compare\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,1737)),"C:\\Projects\\augment_code_v0_compare\\app\\not-found.tsx"]}],d=["C:\\Projects\\augment_code_v0_compare\\app\\unauthorized\\page.tsx"],p="/unauthorized/page",x={require:r,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/unauthorized/page",pathname:"/unauthorized",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4369:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var n=r(4699),s=r(2804),a=r.n(s),i=r(2600),o=r(3725);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,o.Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]),c=(0,o.Z)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);var d=r(5958);function p(){return n.jsx(i.A,{children:n.jsx("div",{className:"min-h-[60vh] flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,n.jsxs)("div",{className:"mb-8",children:[n.jsx("div",{className:"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:n.jsx(l,{size:32,className:"text-red-600"})}),n.jsx("h1",{className:"text-3xl font-bold text-primary-900 mb-4",children:"Unauthorized Access"}),n.jsx("p",{className:"text-primary-600 leading-relaxed",children:"You don't have permission to access this page. Please sign in with an authorized account to continue."})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)(a(),{href:"/signin",className:"btn-primary inline-flex items-center space-x-2",children:[n.jsx(c,{size:16}),n.jsx("span",{children:"Sign In"})]}),(0,n.jsxs)(a(),{href:"/",className:"btn-secondary inline-flex items-center space-x-2 ml-4",children:[n.jsx(d.Z,{size:16}),n.jsx("span",{children:"Go Home"})]})]})]})})})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[36,737],()=>r(6361));module.exports=n})();