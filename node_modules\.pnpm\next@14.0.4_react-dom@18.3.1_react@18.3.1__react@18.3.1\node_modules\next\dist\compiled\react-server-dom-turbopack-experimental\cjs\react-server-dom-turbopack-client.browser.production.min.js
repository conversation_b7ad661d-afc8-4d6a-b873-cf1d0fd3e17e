/*
 React
 react-server-dom-turbopack-client.browser.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var p=require("react-dom"),q=require("react"),r={stream:!0};function u(a,b){if(a){var c=a[b[0]];if(a=c[b[2]])c=a.name;else{a=c["*"];if(!a)throw Error('Could not find the module "'+b[0]+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');c=b[2]}return 4===b.length?[a.id,a.chunks,c,1]:[a.id,a.chunks,c]}return b}var w=new Map;
function x(a){var b=__turbopack_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function y(){}
function z(a){for(var b=a[1],c=[],d=0;d<b.length;d++){var f=b[d],h=w.get(f);if(void 0===h){h=__turbopack_load__(f);c.push(h);var e=w.set.bind(w,f,null);h.then(e,y);w.set(f,h)}else null!==h&&c.push(h)}return 4===a.length?0===c.length?x(a[0]):Promise.all(c).then(function(){return x(a[0])}):0<c.length?Promise.all(c):null}
var A=p.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,B=Symbol.for("react.element"),aa=Symbol.for("react.provider"),ba=Symbol.for("react.server_context"),ca=Symbol.for("react.lazy"),C=Symbol.for("react.default_value"),da=Symbol.for("react.postpone"),D=Symbol.iterator;function ea(a){if(null===a||"object"!==typeof a)return null;a=D&&a[D]||a["@@iterator"];return"function"===typeof a?a:null}var fa=Array.isArray,F=Object.getPrototypeOf,ha=Object.prototype,G=new WeakMap;
function ia(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function ja(a,b,c,d){function f(k,g){if(null===g)return null;if("object"===typeof g){if("function"===typeof g.then){null===l&&(l=new FormData);e++;var n=h++;g.then(function(m){m=JSON.stringify(m,f);var t=l;t.append(b+n,m);e--;0===e&&c(t)},function(m){d(m)});return"$@"+n.toString(16)}if(fa(g))return g;if(g instanceof FormData){null===l&&(l=new FormData);var v=l;k=h++;var E=b+k+"_";g.forEach(function(m,t){v.append(E+t,m)});return"$K"+k.toString(16)}if(g instanceof Map)return g=JSON.stringify(Array.from(g),
f),null===l&&(l=new FormData),k=h++,l.append(b+k,g),"$Q"+k.toString(16);if(g instanceof Set)return g=JSON.stringify(Array.from(g),f),null===l&&(l=new FormData),k=h++,l.append(b+k,g),"$W"+k.toString(16);if(ea(g))return Array.from(g);k=F(g);if(k!==ha&&(null===k||null!==F(k)))throw Error("Only plain objects, and a few built-ins, can be passed to Server Actions. Classes or null prototypes are not supported.");return g}if("string"===typeof g){if("Z"===g[g.length-1]&&this[k]instanceof Date)return"$D"+g;
g="$"===g[0]?"$"+g:g;return g}if("boolean"===typeof g)return g;if("number"===typeof g)return ia(g);if("undefined"===typeof g)return"$undefined";if("function"===typeof g){g=G.get(g);if(void 0!==g)return g=JSON.stringify(g,f),null===l&&(l=new FormData),k=h++,l.set(b+k,g),"$F"+k.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof g){k=g.description;if(Symbol.for(k)!==g)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+
(g.description+") cannot be found among global symbols."));return"$S"+k}if("bigint"===typeof g)return"$n"+g.toString(10);throw Error("Type "+typeof g+" is not supported as an argument to a Server Function.");}var h=1,e=0,l=null;a=JSON.stringify(a,f);null===l?c(a):(l.set(b+"0",a),0===e&&c(l))}function H(a,b){G.set(a,b)}var I=q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ContextRegistry;function J(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}J.prototype=Object.create(Promise.prototype);
J.prototype.then=function(a,b){switch(this.status){case "resolved_model":K(this);break;case "resolved_module":L(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":case "cyclic":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function ka(a){switch(a.status){case "resolved_model":K(a);break;case "resolved_module":L(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":case "cyclic":throw a;default:throw a.reason;}}function M(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}function N(a,b,c){switch(a.status){case "fulfilled":M(b,a.value);break;case "pending":case "blocked":case "cyclic":a.value=b;a.reason=c;break;case "rejected":c&&M(c,a.reason)}}
function O(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&M(c,b)}}function P(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.value,d=a.reason;a.status="resolved_module";a.value=b;null!==c&&(L(a),N(a,c,d))}}var Q=null,R=null;
function K(a){var b=Q,c=R;Q=a;R=null;var d=a.value;a.status="cyclic";a.value=null;a.reason=null;try{var f=JSON.parse(d,a._response._fromJSON);if(null!==R&&0<R.deps)R.value=f,a.status="blocked",a.value=null,a.reason=null;else{var h=a.value;a.status="fulfilled";a.value=f;null!==h&&M(h,f)}}catch(e){a.status="rejected",a.reason=e}finally{Q=b,R=c}}
function L(a){try{var b=a.value,c=__turbopack_require__(b[0]);if(4===b.length&&"function"===typeof c.then)if("fulfilled"===c.status)c=c.value;else throw c.reason;var d="*"===b[2]?c:""===b[2]?c.__esModule?c.default:c:c[b[2]];a.status="fulfilled";a.value=d}catch(f){a.status="rejected",a.reason=f}}function S(a,b){a._chunks.forEach(function(c){"pending"===c.status&&O(c,b)})}function T(a,b){var c=a._chunks,d=c.get(b);d||(d=new J("pending",null,null,a),c.set(b,d));return d}
function la(a,b,c,d){if(R){var f=R;d||f.deps++}else f=R={deps:d?0:1,value:null};return function(h){b[c]=h;f.deps--;0===f.deps&&"blocked"===a.status&&(h=a.value,a.status="fulfilled",a.value=f.value,null!==h&&M(h,f.value))}}function ma(a){return function(b){return O(a,b)}}
function na(a,b){function c(){var f=Array.prototype.slice.call(arguments),h=b.bound;return h?"fulfilled"===h.status?d(b.id,h.value.concat(f)):Promise.resolve(h).then(function(e){return d(b.id,e.concat(f))}):d(b.id,f)}var d=a._callServer;H(c,b);return c}function U(a,b){a=T(a,b);switch(a.status){case "resolved_model":K(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function oa(a,b,c,d){if("$"===d[0]){if("$"===d)return B;switch(d[1]){case "$":return d.slice(1);case "L":return b=parseInt(d.slice(2),16),a=T(a,b),{$$typeof:ca,_payload:a,_init:ka};case "@":return b=parseInt(d.slice(2),16),T(a,b);case "S":return Symbol.for(d.slice(2));case "P":return a=d.slice(2),I[a]||(b={$$typeof:ba,_currentValue:C,_currentValue2:C,_defaultValue:C,_threadCount:0,Provider:null,Consumer:null,_globalName:a},b.Provider={$$typeof:aa,_context:b},I[a]=b),I[a].Provider;case "F":return b=
parseInt(d.slice(2),16),b=U(a,b),na(a,b);case "Q":return b=parseInt(d.slice(2),16),a=U(a,b),new Map(a);case "W":return b=parseInt(d.slice(2),16),a=U(a,b),new Set(a);case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=T(a,d);switch(a.status){case "resolved_model":K(a);break;case "resolved_module":L(a)}switch(a.status){case "fulfilled":return a.value;
case "pending":case "blocked":case "cyclic":return d=Q,a.then(la(d,b,c,"cyclic"===a.status),ma(d)),null;default:throw a.reason;}}}return d}function pa(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}
function V(a,b,c,d){var f=new Map;a={_bundlerConfig:a,_moduleLoading:b,_callServer:void 0!==c?c:pa,_nonce:d,_chunks:f,_stringDecoder:new TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};a._fromJSON=qa(a);return a}function W(a,b,c){a._chunks.set(b,new J("fulfilled",c,null,a))}
function ra(a,b,c){var d=a._chunks,f=d.get(b);c=JSON.parse(c,a._fromJSON);var h=u(a._bundlerConfig,c);if(c=z(h)){if(f){var e=f;e.status="blocked"}else e=new J("blocked",null,null,a),d.set(b,e);c.then(function(){return P(e,h)},function(l){return O(e,l)})}else f?P(f,h):d.set(b,new J("resolved_module",h,null,a))}function X(a,b){for(var c=a.length,d=b.length,f=0;f<c;f++)d+=a[f].byteLength;d=new Uint8Array(d);for(var h=f=0;h<c;h++){var e=a[h];d.set(e,f);f+=e.byteLength}d.set(b,f);return d}
function Y(a,b,c,d,f,h){c=0===c.length&&0===d.byteOffset%h?d:X(c,d);f=new f(c.buffer,c.byteOffset,c.byteLength/h);W(a,b,f)}
function sa(a,b,c,d,f){switch(c){case 65:W(a,b,X(d,f).buffer);return;case 67:Y(a,b,d,f,Int8Array,1);return;case 99:W(a,b,0===d.length?f:X(d,f));return;case 85:Y(a,b,d,f,Uint8ClampedArray,1);return;case 83:Y(a,b,d,f,Int16Array,2);return;case 115:Y(a,b,d,f,Uint16Array,2);return;case 76:Y(a,b,d,f,Int32Array,4);return;case 108:Y(a,b,d,f,Uint32Array,4);return;case 70:Y(a,b,d,f,Float32Array,4);return;case 68:Y(a,b,d,f,Float64Array,8);return;case 78:Y(a,b,d,f,BigInt64Array,8);return;case 109:Y(a,b,d,f,BigUint64Array,
8);return;case 86:Y(a,b,d,f,DataView,1);return}for(var h=a._stringDecoder,e="",l=0;l<d.length;l++)e+=h.decode(d[l],r);e+=h.decode(f);switch(c){case 73:ra(a,b,e);break;case 72:b=e[0];e=e.slice(1);a=JSON.parse(e,a._fromJSON);if(e=A.current)switch(b){case "D":e.prefetchDNS(a);break;case "C":"string"===typeof a?e.preconnect(a):e.preconnect(a[0],a[1]);break;case "L":b=a[0];c=a[1];3===a.length?e.preload(b,c,a[2]):e.preload(b,c);break;case "m":"string"===typeof a?e.preloadModule(a):e.preloadModule(a[0],
a[1]);break;case "S":"string"===typeof a?e.preinitStyle(a):e.preinitStyle(a[0],0===a[1]?void 0:a[1],3===a.length?a[2]:void 0);break;case "X":"string"===typeof a?e.preinitScript(a):e.preinitScript(a[0],a[1]);break;case "M":"string"===typeof a?e.preinitModuleScript(a):e.preinitModuleScript(a[0],a[1])}break;case 69:c=JSON.parse(e).digest;e=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
e.stack="Error: "+e.message;e.digest=c;c=a._chunks;(d=c.get(b))?O(d,e):c.set(b,new J("rejected",null,e,a));break;case 84:a._chunks.set(b,new J("fulfilled",e,null,a));break;case 80:e=Error("A Server Component was postponed. The reason is omitted in production builds to avoid leaking sensitive details.");e.$$typeof=da;e.stack="Error: "+e.message;c=a._chunks;(d=c.get(b))?O(d,e):c.set(b,new J("rejected",null,e,a));break;default:d=a._chunks,(c=d.get(b))?"pending"===c.status&&(a=c.value,b=c.reason,c.status=
"resolved_model",c.value=e,null!==a&&(K(c),N(c,a,b))):d.set(b,new J("resolved_model",e,null,a))}}function qa(a){return function(b,c){return"string"===typeof c?oa(a,this,b,c):"object"===typeof c&&null!==c?(b=c[0]===B?{$$typeof:B,type:c[1],key:c[2],ref:null,props:c[3],_owner:null}:c,b):c}}
function Z(a,b){function c(h){var e=h.value;if(h.done)S(a,Error("Connection closed."));else{var l=0,k=a._rowState;h=a._rowID;for(var g=a._rowTag,n=a._rowLength,v=a._buffer,E=e.length;l<E;){var m=-1;switch(k){case 0:m=e[l++];58===m?k=1:h=h<<4|(96<m?m-87:m-48);continue;case 1:k=e[l];84===k||65===k||67===k||99===k||85===k||83===k||115===k||76===k||108===k||70===k||68===k||78===k||109===k||86===k?(g=k,k=2,l++):64<k&&91>k?(g=k,k=3,l++):(g=0,k=3);continue;case 2:m=e[l++];44===m?k=4:n=n<<4|(96<m?m-87:m-
48);continue;case 3:m=e.indexOf(10,l);break;case 4:m=l+n,m>e.length&&(m=-1)}var t=e.byteOffset+l;if(-1<m)n=new Uint8Array(e.buffer,t,m-l),sa(a,h,g,v,n),l=m,3===k&&l++,n=h=g=k=0,v.length=0;else{e=new Uint8Array(e.buffer,t,e.byteLength-l);v.push(e);n-=e.byteLength;break}}a._rowState=k;a._rowID=h;a._rowTag=g;a._rowLength=n;return f.read().then(c).catch(d)}}function d(h){S(a,h)}var f=b.getReader();f.read().then(c).catch(d)}
exports.createFromFetch=function(a,b){var c=V(null,null,b&&b.callServer?b.callServer:void 0,void 0);a.then(function(d){Z(c,d.body)},function(d){S(c,d)});return T(c,0)};exports.createFromReadableStream=function(a,b){b=V(null,null,b&&b.callServer?b.callServer:void 0,void 0);Z(b,a);return T(b,0)};exports.createServerReference=function(a,b){function c(){var d=Array.prototype.slice.call(arguments);return b(a,d)}H(c,{id:a,bound:null});return c};
exports.encodeReply=function(a){return new Promise(function(b,c){ja(a,"",b,c)})};

//# sourceMappingURL=react-server-dom-turbopack-client.browser.production.min.js.map
