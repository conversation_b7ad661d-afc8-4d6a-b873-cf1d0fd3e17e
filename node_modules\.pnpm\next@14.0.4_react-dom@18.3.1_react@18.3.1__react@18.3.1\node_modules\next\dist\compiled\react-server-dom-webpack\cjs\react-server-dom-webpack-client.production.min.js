/**
 * @license React
 * react-server-dom-webpack-client.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var h=require("react"),l={stream:!0};function m(a,b){return a?(a=a[b.id][b.name],b.async?{id:a.id,chunks:a.chunks,name:a.name,async:!0}:a):b}var n=new Map,p=new Map;function q(){}
function r(a){for(var b=a.chunks,c=[],d=0;d<b.length;d++){var e=b[d],f=n.get(e);if(void 0===f){f=globalThis.__next_chunk_load__(e);c.push(f);var k=n.set.bind(n,e,null);f.then(k,q);n.set(e,f)}else null!==f&&c.push(f)}if(a.async){if(b=p.get(a.id))return"fulfilled"===b.status?null:b;var g=Promise.all(c).then(function(){return globalThis.__next_require__(a.id)});g.then(function(a){g.status="fulfilled";g.value=a},function(a){g.status="rejected";g.reason=a});p.set(a.id,g);return g}return 0<c.length?Promise.all(c):null}
var t=Symbol.for("react.element"),u=Symbol.for("react.lazy"),v=Symbol.for("react.default_value"),w=h.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ContextRegistry;function x(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}x.prototype=Object.create(Promise.prototype);
x.prototype.then=function(a,b){switch(this.status){case "resolved_model":y(this);break;case "resolved_module":z(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function B(a){switch(a.status){case "resolved_model":y(a);break;case "resolved_module":z(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":throw a;default:throw a.reason;}}function C(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}function D(a,b,c){switch(a.status){case "fulfilled":C(b,a.value);break;case "pending":case "blocked":a.value=b;a.reason=c;break;case "rejected":c&&C(c,a.reason)}}
function E(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&C(c,b)}}function F(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.value,d=a.reason;a.status="resolved_module";a.value=b;null!==c&&(z(a),D(a,c,d))}}var G=null,H=null;
function y(a){var b=G,c=H;G=a;H=null;try{var d=JSON.parse(a.value,a._response._fromJSON);null!==H&&0<H.deps?(H.value=d,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=d)}catch(e){a.status="rejected",a.reason=e}finally{G=b,H=c}}
function z(a){try{var b=a.value;if(b.async){var c=p.get(b.id);if("fulfilled"===c.status)var d=c.value;else throw c.reason;}else d=globalThis.__next_require__(b.id);var e="*"===b.name?d:""===b.name?d.__esModule?d.default:d:d[b.name];a.status="fulfilled";a.value=e}catch(f){a.status="rejected",a.reason=f}}function I(a,b){a._chunks.forEach(function(a){"pending"===a.status&&E(a,b)})}function J(a,b){var c=a._chunks,d=c.get(b);d||(d=new x("pending",null,null,a),c.set(b,d));return d}
function K(a,b,c){if(H){var d=H;d.deps++}else d=H={deps:1,value:null};return function(e){b[c]=e;d.deps--;0===d.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=d.value,null!==e&&C(e,d.value))}}function M(a){return function(b){return E(a,b)}}
function N(a,b,c,d){if("$"===d[0]){if("$"===d)return t;switch(d[1]){case "$":return d.substring(1);case "L":return b=parseInt(d.substring(2),16),a=J(a,b),{$$typeof:u,_payload:a,_init:B};case "@":return b=parseInt(d.substring(2),16),J(a,b);case "S":return Symbol.for(d.substring(2));case "P":return a=d.substring(2),w[a]||(w[a]=h.createServerContext(a,v)),w[a].Provider;default:d=parseInt(d.substring(1),16);a=J(a,d);switch(a.status){case "resolved_model":y(a);break;case "resolved_module":z(a)}switch(a.status){case "fulfilled":return a.value;
case "pending":case "blocked":return d=G,a.then(K(d,b,c),M(d)),null;default:throw a.reason;}}}return d}function O(a,b,c){var d=a._chunks,e=d.get(b);c=JSON.parse(c,a._fromJSON);var f=m(a._bundlerConfig,c);if(c=r(f)){if(e){var k=e;k.status="blocked"}else k=new x("blocked",null,null,a),d.set(b,k);c.then(function(){return F(k,f)},function(a){return E(k,a)})}else e?F(e,f):d.set(b,new x("resolved_module",f,null,a))}function P(a){I(a,Error("Connection closed."))}
function Q(a,b){if(""!==b){var c=b.indexOf(":",0),d=parseInt(b.substring(0,c),16);switch(b[c+1]){case "I":O(a,d,b.substring(c+2));break;case "E":c=JSON.parse(b.substring(c+2)).digest;b=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");b.stack="Error: "+b.message;b.digest=c;c=a._chunks;
var e=c.get(d);e?E(e,b):c.set(d,new x("rejected",null,b,a));break;default:b=b.substring(c+1),e=a._chunks,(c=e.get(d))?"pending"===c.status&&(a=c.value,d=c.reason,c.status="resolved_model",c.value=b,null!==a&&(y(c),D(c,a,d))):e.set(d,new x("resolved_model",b,null,a))}}}function R(a){return function(b,c){return"string"===typeof c?N(a,this,b,c):"object"===typeof c&&null!==c?(b=c[0]===t?{$$typeof:t,type:c[1],key:c[2],ref:null,props:c[3],_owner:null}:c,b):c}}
function S(a){var b=new TextDecoder,c=new Map;a={_bundlerConfig:a,_chunks:c,_partialRow:"",_stringDecoder:b};a._fromJSON=R(a);return a}function T(a,b){function c(b){var f=b.value;if(b.done)P(a);else{b=f;f=a._stringDecoder;for(var g=b.indexOf(10);-1<g;){var L=a._partialRow;var A=b.subarray(0,g);A=f.decode(A);Q(a,L+A);a._partialRow="";b=b.subarray(g+1);g=b.indexOf(10)}a._partialRow+=f.decode(b,l);return e.read().then(c).catch(d)}}function d(b){I(a,b)}var e=b.getReader();e.read().then(c).catch(d)}
exports.createFromFetch=function(a,b){var c=S(b&&b.moduleMap?b.moduleMap:null);a.then(function(a){T(c,a.body)},function(a){I(c,a)});return J(c,0)};exports.createFromReadableStream=function(a,b){b=S(b&&b.moduleMap?b.moduleMap:null);T(b,a);return J(b,0)};
exports.createFromXHR=function(a,b){function c(){for(var b=a.responseText,c=f,d=b.indexOf("\n",c);-1<d;)c=e._partialRow+b.substring(c,d),Q(e,c),e._partialRow="",c=d+1,d=b.indexOf("\n",c);e._partialRow+=b.substring(c);f=b.length}function d(){I(e,new TypeError("Network error"))}var e=S(b&&b.moduleMap?b.moduleMap:null),f=0;a.addEventListener("progress",c);a.addEventListener("load",function(){c();P(e)});a.addEventListener("error",d);a.addEventListener("abort",d);a.addEventListener("timeout",d);return J(e,
0)};
