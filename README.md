# St Cloud Enterprises Portal

A modern Next.js website for managing access to website properties owned by St Cloud Enterprises.

## Features

- **Responsive Design**: Built with Tailwind CSS for mobile-first responsive design
- **Authentication System**: Simple authentication with protected routes
- **Property Management**: Dashboard with property cards showing managed websites
- **Configurable Colors**: Easy color theme switching (currently using Zinc palette)
- **Loading States**: Smart loading indicators for pages (1s+) and actions (500ms+)
- **Error Handling**: Dedicated pages for 404, unauthorized access, and errors
- **Legal Pages**: Privacy Policy and Terms of Service with markdown-compatible formatting
- **Geist Font**: Modern typography using Vercel's <PERSON>ei<PERSON> font family

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm, yarn, or pnpm

### Installation

**Option 1: Using Batch Files (Recommended for Windows)**
1. Double-click `install-and-build.bat` to install dependencies and build
2. Double-click `dev.bat` to start the development server
3. Open [http://localhost:3000](http://localhost:3000) in your browser

**Option 2: Using Command Prompt**
1. Open Command Prompt (cmd) - NOT PowerShell
2. Navigate to the project directory
3. Run: `npm install --no-optional --legacy-peer-deps`
4. Run: `npm run dev`
5. Open [http://localhost:3000](http://localhost:3000) in your browser

**Option 3: Using PowerShell (if execution policy allows)**
```bash
npm install
npm run dev
```

## Troubleshooting Build Issues

If you encounter build errors, try these solutions in order:

### 1. Clear Cache and Reinstall
```cmd
npm cache clean --force
del /s /q node_modules package-lock.json
npm install --force
```

### 2. Font Issues
If you get Geist font errors:
- The project uses Geist font from Vercel
- If build fails, the font will fallback to system fonts
- Make sure `geist` package is installed: `npm install geist@latest`

### 3. TypeScript Errors
```cmd
npm run build -- --no-lint
```

### 4. Dependency Conflicts
```cmd
npm install --legacy-peer-deps --force
```

### 5. Windows-Specific Issues
- Use Command Prompt instead of PowerShell
- Run as Administrator if needed
- Use the provided `.bat` files for easier execution

### Demo Credentials

For testing the authentication system:
- **Email**: <EMAIL>
- **Password**: password

## Project Structure

```
├── app/                    # Next.js 13+ App Router
│   ├── account/           # Account management page
│   ├── dashboard/         # Property dashboard (protected)
│   ├── privacy/           # Privacy Policy page
│   ├── signin/            # Sign in page
│   ├── terms/             # Terms of Service page
│   ├── unauthorized/      # Unauthorized access page
│   ├── error.tsx          # Global error page
│   ├── layout.tsx         # Root layout
│   ├── not-found.tsx      # 404 page
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   ├── Footer.tsx         # Site footer
│   ├── Layout.tsx         # Page layout wrapper
│   ├── LoadingSpinner.tsx # Loading indicators
│   ├── Navigation.tsx     # Main navigation
│   └── PropertyCard.tsx   # Property display cards
├── contexts/              # React contexts
│   └── AuthContext.tsx    # Authentication context
└── lib/                   # Utilities and configuration
    ├── auth.ts            # Authentication utilities
    └── config.ts          # Site configuration and data
```

## Configuration

### Color Themes

The website uses a configurable color system. To change the color palette:

1. Edit `lib/config.ts` and change the `currentTheme` value
2. Available themes: `zinc`, `slate`, `blue` (or add your own)
3. The CSS variables in `app/globals.css` will automatically update

### Site Configuration

Update site information in `lib/config.ts`:
- Site name and description
- Mission statement
- Sample properties data

## Authentication

The current implementation uses a simple mock authentication system for demonstration. In production, you would integrate with:
- NextAuth.js
- Auth0
- Firebase Auth
- Supabase Auth
- Or your preferred authentication provider

## Deployment

The easiest way to deploy is using [Vercel](https://vercel.com/):

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

For other platforms, build the project:

```bash
npm run build
npm start
```

## License

This project is private and proprietary to St Cloud Enterprises.
