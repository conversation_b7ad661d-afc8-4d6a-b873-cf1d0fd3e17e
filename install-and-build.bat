@echo off
echo Installing dependencies...
call npm install --no-optional --legacy-peer-deps

if %errorlevel% neq 0 (
    echo.
    echo Installation failed! Trying alternative approach...
    call npm install --force
)

echo.
echo Building project...
call npm run build

if %errorlevel% neq 0 (
    echo.
    echo Build failed! Check the errors above.
    pause
    exit /b 1
)

echo.
echo Build complete!
pause
