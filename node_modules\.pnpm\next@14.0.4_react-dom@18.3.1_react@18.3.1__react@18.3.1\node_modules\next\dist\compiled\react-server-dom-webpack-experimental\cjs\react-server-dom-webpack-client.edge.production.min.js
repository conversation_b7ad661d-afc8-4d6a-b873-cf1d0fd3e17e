/*
 React
 react-server-dom-webpack-client.edge.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var p=require("react-dom"),q=require("react"),r={stream:!0};function aa(a,b){if(a){var c=a[b[0]];if(a=c[b[2]])c=a.name;else{a=c["*"];if(!a)throw Error('Could not find the module "'+b[0]+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');c=b[2]}return 4===b.length?[a.id,a.chunks,c,1]:[a.id,a.chunks,c]}return b}var u=new Map;
function v(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function ba(){}
function ca(a){for(var b=a[1],c=[],d=0;d<b.length;){var e=b[d++];b[d++];var g=u.get(e);if(void 0===g){g=__webpack_chunk_load__(e);c.push(g);var f=u.set.bind(u,e,null);g.then(f,ba);u.set(e,g)}else null!==g&&c.push(g)}return 4===a.length?0===c.length?v(a[0]):Promise.all(c).then(function(){return v(a[0])}):0<c.length?Promise.all(c):null}
function da(a,b,c){if(null!==a)for(var d=1;d<b.length;d+=2){var e=c,g=x.current;if(g){var f=g.preinitScript,l=a.prefix+b[d];var h=a.crossOrigin;h="string"===typeof h?"use-credentials"===h?h:"":void 0;f.call(g,l,{crossOrigin:h,nonce:e})}}}
var x=p.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,y=Symbol.for("react.element"),ea=Symbol.for("react.provider"),fa=Symbol.for("react.server_context"),ha=Symbol.for("react.lazy"),z=Symbol.for("react.default_value"),ia=Symbol.for("react.postpone"),A=Symbol.iterator;function ja(a){if(null===a||"object"!==typeof a)return null;a=A&&a[A]||a["@@iterator"];return"function"===typeof a?a:null}var ka=Array.isArray,B=Object.getPrototypeOf,la=Object.prototype,C=new WeakMap;
function ma(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function D(a,b,c,d){function e(h,k){if(null===k)return null;if("object"===typeof k){if("function"===typeof k.then){null===l&&(l=new FormData);f++;var n=g++;k.then(function(m){m=JSON.stringify(m,e);var t=l;t.append(b+n,m);f--;0===f&&c(t)},function(m){d(m)});return"$@"+n.toString(16)}if(ka(k))return k;if(k instanceof FormData){null===l&&(l=new FormData);var w=l;h=g++;var F=b+h+"_";k.forEach(function(m,t){w.append(F+t,m)});return"$K"+h.toString(16)}if(k instanceof Map)return k=JSON.stringify(Array.from(k),
e),null===l&&(l=new FormData),h=g++,l.append(b+h,k),"$Q"+h.toString(16);if(k instanceof Set)return k=JSON.stringify(Array.from(k),e),null===l&&(l=new FormData),h=g++,l.append(b+h,k),"$W"+h.toString(16);if(ja(k))return Array.from(k);h=B(k);if(h!==la&&(null===h||null!==B(h)))throw Error("Only plain objects, and a few built-ins, can be passed to Server Actions. Classes or null prototypes are not supported.");return k}if("string"===typeof k){if("Z"===k[k.length-1]&&this[h]instanceof Date)return"$D"+k;
k="$"===k[0]?"$"+k:k;return k}if("boolean"===typeof k)return k;if("number"===typeof k)return ma(k);if("undefined"===typeof k)return"$undefined";if("function"===typeof k){k=C.get(k);if(void 0!==k)return k=JSON.stringify(k,e),null===l&&(l=new FormData),h=g++,l.set(b+h,k),"$F"+h.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof k){h=k.description;if(Symbol.for(h)!==k)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+
(k.description+") cannot be found among global symbols."));return"$S"+h}if("bigint"===typeof k)return"$n"+k.toString(10);throw Error("Type "+typeof k+" is not supported as an argument to a Server Function.");}var g=1,f=0,l=null;a=JSON.stringify(a,e);null===l?c(a):(l.set(b+"0",a),0===f&&c(l))}var E=new WeakMap;
function na(a){var b,c,d=new Promise(function(e,g){b=e;c=g});D(a,"",function(e){if("string"===typeof e){var g=new FormData;g.append("0",e);e=g}d.status="fulfilled";d.value=e;b(e)},function(e){d.status="rejected";d.reason=e;c(e)});return d}
function oa(a){var b=C.get(this);if(!b)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var c=null;if(null!==b.bound){c=E.get(b);c||(c=na(b),E.set(b,c));if("rejected"===c.status)throw c.reason;if("fulfilled"!==c.status)throw c;b=c.value;var d=new FormData;b.forEach(function(e,g){d.append("$ACTION_"+a+":"+g,e)});c=d;b="$ACTION_REF_"+a}else b="$ACTION_ID_"+b.id;return{name:b,method:"POST",encType:"multipart/form-data",data:c}}
function pa(a,b){var c=C.get(this);if(!c)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(c.id!==a)return!1;var d=c.bound;if(null===d)return 0===b;switch(d.status){case "fulfilled":return d.value.length===b;case "pending":throw d;case "rejected":throw d.reason;default:throw"string"!==typeof d.status&&(d.status="pending",d.then(function(e){d.status="fulfilled";d.value=e},function(e){d.status="rejected";d.reason=e})),d;}}
function G(a,b){Object.defineProperties(a,{$$FORM_ACTION:{value:oa},$$IS_SIGNATURE_EQUAL:{value:pa},bind:{value:qa}});C.set(a,b)}var ra=Function.prototype.bind,sa=Array.prototype.slice;function qa(){var a=ra.apply(this,arguments),b=C.get(this);if(b){var c=sa.call(arguments,1),d=null;d=null!==b.bound?Promise.resolve(b.bound).then(function(e){return e.concat(c)}):Promise.resolve(c);G(a,{id:b.id,bound:d})}return a}
function ta(a,b){function c(){var d=Array.prototype.slice.call(arguments);return b(a,d)}G(c,{id:a,bound:null});return c}var H=q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ContextRegistry;function I(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}I.prototype=Object.create(Promise.prototype);
I.prototype.then=function(a,b){switch(this.status){case "resolved_model":J(this);break;case "resolved_module":K(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":case "cyclic":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function ua(a){switch(a.status){case "resolved_model":J(a);break;case "resolved_module":K(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":case "cyclic":throw a;default:throw a.reason;}}function L(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}function M(a,b,c){switch(a.status){case "fulfilled":L(b,a.value);break;case "pending":case "blocked":case "cyclic":a.value=b;a.reason=c;break;case "rejected":c&&L(c,a.reason)}}
function N(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&L(c,b)}}function O(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.value,d=a.reason;a.status="resolved_module";a.value=b;null!==c&&(K(a),M(a,c,d))}}var P=null,Q=null;
function J(a){var b=P,c=Q;P=a;Q=null;var d=a.value;a.status="cyclic";a.value=null;a.reason=null;try{var e=JSON.parse(d,a._response._fromJSON);if(null!==Q&&0<Q.deps)Q.value=e,a.status="blocked",a.value=null,a.reason=null;else{var g=a.value;a.status="fulfilled";a.value=e;null!==g&&L(g,e)}}catch(f){a.status="rejected",a.reason=f}finally{P=b,Q=c}}
function K(a){try{var b=a.value,c=globalThis.__next_require__(b[0]);if(4===b.length&&"function"===typeof c.then)if("fulfilled"===c.status)c=c.value;else throw c.reason;var d="*"===b[2]?c:""===b[2]?c.__esModule?c.default:c:c[b[2]];a.status="fulfilled";a.value=d}catch(e){a.status="rejected",a.reason=e}}function R(a,b){a._chunks.forEach(function(c){"pending"===c.status&&N(c,b)})}function S(a,b){var c=a._chunks,d=c.get(b);d||(d=new I("pending",null,null,a),c.set(b,d));return d}
function va(a,b,c,d){if(Q){var e=Q;d||e.deps++}else e=Q={deps:d?0:1,value:null};return function(g){b[c]=g;e.deps--;0===e.deps&&"blocked"===a.status&&(g=a.value,a.status="fulfilled",a.value=e.value,null!==g&&L(g,e.value))}}function wa(a){return function(b){return N(a,b)}}
function xa(a,b){function c(){var e=Array.prototype.slice.call(arguments),g=b.bound;return g?"fulfilled"===g.status?d(b.id,g.value.concat(e)):Promise.resolve(g).then(function(f){return d(b.id,f.concat(e))}):d(b.id,e)}var d=a._callServer;G(c,b);return c}function T(a,b){a=S(a,b);switch(a.status){case "resolved_model":J(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function ya(a,b,c,d){if("$"===d[0]){if("$"===d)return y;switch(d[1]){case "$":return d.slice(1);case "L":return b=parseInt(d.slice(2),16),a=S(a,b),{$$typeof:ha,_payload:a,_init:ua};case "@":return b=parseInt(d.slice(2),16),S(a,b);case "S":return Symbol.for(d.slice(2));case "P":return a=d.slice(2),H[a]||(b={$$typeof:fa,_currentValue:z,_currentValue2:z,_defaultValue:z,_threadCount:0,Provider:null,Consumer:null,_globalName:a},b.Provider={$$typeof:ea,_context:b},H[a]=b),H[a].Provider;case "F":return b=
parseInt(d.slice(2),16),b=T(a,b),xa(a,b);case "Q":return b=parseInt(d.slice(2),16),a=T(a,b),new Map(a);case "W":return b=parseInt(d.slice(2),16),a=T(a,b),new Set(a);case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=S(a,d);switch(a.status){case "resolved_model":J(a);break;case "resolved_module":K(a)}switch(a.status){case "fulfilled":return a.value;
case "pending":case "blocked":case "cyclic":return d=P,a.then(va(d,b,c,"cyclic"===a.status),wa(d)),null;default:throw a.reason;}}}return d}function za(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}function U(a,b,c){a._chunks.set(b,new I("fulfilled",c,null,a))}
function Aa(a,b,c){var d=a._chunks,e=d.get(b);c=JSON.parse(c,a._fromJSON);var g=aa(a._bundlerConfig,c);da(a._moduleLoading,c[1],a._nonce);if(c=ca(g)){if(e){var f=e;f.status="blocked"}else f=new I("blocked",null,null,a),d.set(b,f);c.then(function(){return O(f,g)},function(l){return N(f,l)})}else e?O(e,g):d.set(b,new I("resolved_module",g,null,a))}
function V(a,b){for(var c=a.length,d=b.length,e=0;e<c;e++)d+=a[e].byteLength;d=new Uint8Array(d);for(var g=e=0;g<c;g++){var f=a[g];d.set(f,e);e+=f.byteLength}d.set(b,e);return d}function W(a,b,c,d,e,g){c=0===c.length&&0===d.byteOffset%g?d:V(c,d);e=new e(c.buffer,c.byteOffset,c.byteLength/g);U(a,b,e)}
function Ba(a,b,c,d,e){switch(c){case 65:U(a,b,V(d,e).buffer);return;case 67:W(a,b,d,e,Int8Array,1);return;case 99:U(a,b,0===d.length?e:V(d,e));return;case 85:W(a,b,d,e,Uint8ClampedArray,1);return;case 83:W(a,b,d,e,Int16Array,2);return;case 115:W(a,b,d,e,Uint16Array,2);return;case 76:W(a,b,d,e,Int32Array,4);return;case 108:W(a,b,d,e,Uint32Array,4);return;case 70:W(a,b,d,e,Float32Array,4);return;case 68:W(a,b,d,e,Float64Array,8);return;case 78:W(a,b,d,e,BigInt64Array,8);return;case 109:W(a,b,d,e,BigUint64Array,
8);return;case 86:W(a,b,d,e,DataView,1);return}for(var g=a._stringDecoder,f="",l=0;l<d.length;l++)f+=g.decode(d[l],r);f+=g.decode(e);switch(c){case 73:Aa(a,b,f);break;case 72:b=f[0];f=f.slice(1);a=JSON.parse(f,a._fromJSON);if(f=x.current)switch(b){case "D":f.prefetchDNS(a);break;case "C":"string"===typeof a?f.preconnect(a):f.preconnect(a[0],a[1]);break;case "L":b=a[0];c=a[1];3===a.length?f.preload(b,c,a[2]):f.preload(b,c);break;case "m":"string"===typeof a?f.preloadModule(a):f.preloadModule(a[0],
a[1]);break;case "S":"string"===typeof a?f.preinitStyle(a):f.preinitStyle(a[0],0===a[1]?void 0:a[1],3===a.length?a[2]:void 0);break;case "X":"string"===typeof a?f.preinitScript(a):f.preinitScript(a[0],a[1]);break;case "M":"string"===typeof a?f.preinitModuleScript(a):f.preinitModuleScript(a[0],a[1])}break;case 69:c=JSON.parse(f).digest;f=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
f.stack="Error: "+f.message;f.digest=c;c=a._chunks;(d=c.get(b))?N(d,f):c.set(b,new I("rejected",null,f,a));break;case 84:a._chunks.set(b,new I("fulfilled",f,null,a));break;case 80:f=Error("A Server Component was postponed. The reason is omitted in production builds to avoid leaking sensitive details.");f.$$typeof=ia;f.stack="Error: "+f.message;c=a._chunks;(d=c.get(b))?N(d,f):c.set(b,new I("rejected",null,f,a));break;default:d=a._chunks,(c=d.get(b))?"pending"===c.status&&(a=c.value,b=c.reason,c.status=
"resolved_model",c.value=f,null!==a&&(J(c),M(c,a,b))):d.set(b,new I("resolved_model",f,null,a))}}function Ca(a){return function(b,c){return"string"===typeof c?ya(a,this,b,c):"object"===typeof c&&null!==c?(b=c[0]===y?{$$typeof:y,type:c[1],key:c[2],ref:null,props:c[3],_owner:null}:c,b):c}}function X(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.");}
function Y(a){var b=a.ssrManifest.moduleMap,c=a.ssrManifest.moduleLoading;a="string"===typeof a.nonce?a.nonce:void 0;var d=new Map;b={_bundlerConfig:b,_moduleLoading:c,_callServer:void 0!==X?X:za,_nonce:a,_chunks:d,_stringDecoder:new TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};b._fromJSON=Ca(b);return b}
function Z(a,b){function c(g){var f=g.value;if(g.done)R(a,Error("Connection closed."));else{var l=0,h=a._rowState;g=a._rowID;for(var k=a._rowTag,n=a._rowLength,w=a._buffer,F=f.length;l<F;){var m=-1;switch(h){case 0:m=f[l++];58===m?h=1:g=g<<4|(96<m?m-87:m-48);continue;case 1:h=f[l];84===h||65===h||67===h||99===h||85===h||83===h||115===h||76===h||108===h||70===h||68===h||78===h||109===h||86===h?(k=h,h=2,l++):64<h&&91>h?(k=h,h=3,l++):(k=0,h=3);continue;case 2:m=f[l++];44===m?h=4:n=n<<4|(96<m?m-87:m-
48);continue;case 3:m=f.indexOf(10,l);break;case 4:m=l+n,m>f.length&&(m=-1)}var t=f.byteOffset+l;if(-1<m)n=new Uint8Array(f.buffer,t,m-l),Ba(a,g,k,w,n),l=m,3===h&&l++,n=g=k=h=0,w.length=0;else{f=new Uint8Array(f.buffer,t,f.byteLength-l);w.push(f);n-=f.byteLength;break}}a._rowState=h;a._rowID=g;a._rowTag=k;a._rowLength=n;return e.read().then(c).catch(d)}}function d(g){R(a,g)}var e=b.getReader();e.read().then(c).catch(d)}
exports.createFromFetch=function(a,b){var c=Y(b);a.then(function(d){Z(c,d.body)},function(d){R(c,d)});return S(c,0)};exports.createFromReadableStream=function(a,b){b=Y(b);Z(b,a);return S(b,0)};exports.createServerReference=function(a){return ta(a,X)};exports.encodeReply=function(a){return new Promise(function(b,c){D(a,"",b,c)})};

//# sourceMappingURL=react-server-dom-webpack-client.edge.production.min.js.map
