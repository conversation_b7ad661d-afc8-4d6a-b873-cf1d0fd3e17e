@echo off
echo Checking build requirements...

echo.
echo 1. Checking Node.js version...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found! Please install Node.js 18 or higher.
    pause
    exit /b 1
)

echo.
echo 2. Checking npm version...
npm --version
if %errorlevel% neq 0 (
    echo ERROR: npm not found!
    pause
    exit /b 1
)

echo.
echo 3. Checking if package.json exists...
if not exist package.json (
    echo ERROR: package.json not found!
    pause
    exit /b 1
)

echo.
echo 4. Installing dependencies...
call npm install --no-optional --legacy-peer-deps

if %errorlevel% neq 0 (
    echo.
    echo Installation failed! Trying with --force...
    call npm install --force
    if %errorlevel% neq 0 (
        echo ERROR: Could not install dependencies!
        pause
        exit /b 1
    )
)

echo.
echo 5. Running TypeScript check...
call npx tsc --noEmit
if %errorlevel% neq 0 (
    echo WARNING: TypeScript errors found, but continuing...
)

echo.
echo 6. Running build...
call npm run build

if %errorlevel% neq 0 (
    echo.
    echo BUILD FAILED! Common solutions:
    echo - Clear cache: npm cache clean --force
    echo - Delete node_modules and reinstall
    echo - Check for TypeScript errors
    echo - Try: npm install --legacy-peer-deps --force
    pause
    exit /b 1
)

echo.
echo SUCCESS: Build completed successfully!
pause
