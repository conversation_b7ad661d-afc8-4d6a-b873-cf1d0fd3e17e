{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-route-loader/index.ts"], "names": ["stringify", "getModuleBuildInfo", "RouteKind", "normalizePagePath", "decodeFromBase64", "encodeToBase64", "isInstrumentationHookFile", "loadEntrypoint", "getRouteLoaderEntry", "options", "kind", "PAGES", "query", "page", "preferredRegion", "absolutePagePath", "absoluteAppPath", "pages", "absoluteDocumentPath", "middlewareConfigBase64", "middlewareConfig", "PAGES_API", "Error", "loadPages", "buildInfo", "route", "file", "VAR_USERLAND", "VAR_MODULE_DOCUMENT", "VAR_MODULE_APP", "VAR_DEFINITION_PAGE", "VAR_DEFINITION_PATHNAME", "loadPagesAPI", "loader", "_module", "opts", "getOptions"], "mappings": "AAGA,SAASA,SAAS,QAAQ,cAAa;AACvC,SAEEC,kBAAkB,QACb,2BAA0B;AACjC,SAASC,SAAS,QAAQ,uCAAsC;AAChE,SAASC,iBAAiB,QAAQ,uDAAsD;AACxF,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,WAAU;AAC3D,SAASC,yBAAyB,QAAQ,kBAAiB;AAC3D,SAASC,cAAc,QAAQ,2BAA0B;AAsFzD;;;;;CAKC,GACD,OAAO,SAASC,oBAAoBC,OAAgC;IAClE,OAAQA,QAAQC,IAAI;QAClB,KAAKR,UAAUS,KAAK;YAAE;gBACpB,MAAMC,QAAiC;oBACrCF,MAAMD,QAAQC,IAAI;oBAClBG,MAAMJ,QAAQI,IAAI;oBAClBC,iBAAiBL,QAAQK,eAAe;oBACxCC,kBAAkBN,QAAQM,gBAAgB;oBAC1C,uEAAuE;oBACvE,qCAAqC;oBACrCC,iBAAiBP,QAAQQ,KAAK,CAAC,QAAQ;oBACvCC,sBAAsBT,QAAQQ,KAAK,CAAC,aAAa;oBACjDE,wBAAwBd,eAAeI,QAAQW,gBAAgB;gBACjE;gBAEA,OAAO,CAAC,kBAAkB,EAAEpB,UAAUY,OAAO,CAAC,CAAC;YACjD;QACA,KAAKV,UAAUmB,SAAS;YAAE;gBACxB,MAAMT,QAAoC;oBACxCF,MAAMD,QAAQC,IAAI;oBAClBG,MAAMJ,QAAQI,IAAI;oBAClBC,iBAAiBL,QAAQK,eAAe;oBACxCC,kBAAkBN,QAAQM,gBAAgB;oBAC1CI,wBAAwBd,eAAeI,QAAQW,gBAAgB;gBACjE;gBAEA,OAAO,CAAC,kBAAkB,EAAEpB,UAAUY,OAAO,CAAC,CAAC;YACjD;QACA;YAAS;gBACP,MAAM,IAAIU,MAAM;YAClB;IACF;AACF;AAEA,MAAMC,YAAY,OAChB,EACEV,IAAI,EACJE,gBAAgB,EAChBG,oBAAoB,EACpBF,eAAe,EACfF,eAAe,EACfK,sBAAsB,EACE,EAC1BK;IAEA,MAAMJ,mBAAqChB,iBACzCe;IAGF,mCAAmC;IACnCK,UAAUC,KAAK,GAAG;QAChBZ;QACAE;QACAD;QACAM;IACF;IAEA,IAAIM,OAAO,MAAMnB,eAAe,SAAS;QACvCoB,cAAcZ;QACda,qBAAqBV;QACrBW,gBAAgBb;QAChBc,qBAAqB3B,kBAAkBU;QACvCkB,yBAAyBlB;IAC3B;IAEA,IAAIP,0BAA0BO,OAAO;QACnC,8DAA8D;QAC9D,0DAA0D;QAC1D,iDAAiD;QACjDa,QAAQ;IACV;IAEA,OAAOA;AACT;AAEA,MAAMM,eAAe,OACnB,EACEnB,IAAI,EACJE,gBAAgB,EAChBD,eAAe,EACfK,sBAAsB,EACK,EAC7BK;IAEA,MAAMJ,mBAAqChB,iBACzCe;IAGF,mCAAmC;IACnCK,UAAUC,KAAK,GAAG;QAChBZ;QACAE;QACAD;QACAM;IACF;IAEA,OAAO,MAAMb,eAAe,aAAa;QACvCoB,cAAcZ;QACde,qBAAqB3B,kBAAkBU;QACvCkB,yBAAyBlB;IAC3B;AACF;AAEA;;;CAGC,GACD,MAAMoB,SACJ;IACE,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;QACjB,MAAM,IAAIZ,MAAM;IAClB;IAEA,MAAME,YAAYvB,mBAAmB,IAAI,CAACiC,OAAO;IACjD,MAAMC,OAAO,IAAI,CAACC,UAAU;IAE5B,OAAQD,KAAKzB,IAAI;QACf,KAAKR,UAAUS,KAAK;YAAE;gBACpB,OAAO,MAAMY,UAAUY,MAAMX;YAC/B;QACA,KAAKtB,UAAUmB,SAAS;YAAE;gBACxB,OAAO,MAAMW,aAAaG,MAAMX;YAClC;QACA;YAAS;gBACP,MAAM,IAAIF,MAAM;YAClB;IACF;AACF;AAEF,eAAeW,OAAM"}